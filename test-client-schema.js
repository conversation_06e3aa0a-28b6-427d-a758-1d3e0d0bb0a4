// Quick test to verify the CreateClientSchema now accepts company fields
const { z } = require('zod');

// Simulate the schema (simplified version)
const CreateClientSchema = z.object({
  firstName: z.string().min(1),
  lastName: z.string().min(1),
  email: z.string().email(),
  phone: z.string().optional(),
  status: z.enum(["created", "active", "inactive"]).default("created"),
  type: z.literal("client"),
  companyId: z.string().optional(),
  subscriptionId: z.string().optional(),
  // Company creation fields
  companyName: z.string().optional(),
  companyTin: z.string().optional(),
  companyCategory: z.string().optional(),
  companyEmail: z.string().email().optional().or(z.literal("")),
  companyAddress: z.string().optional(),
  companyPhone: z.string().optional(),
});

// Test data that matches what the form sends
const testData = {
  firstName: "John",
  lastName: "Do<PERSON>",
  email: "<EMAIL>",
  phone: "************",
  status: "created",
  type: "client",
  companyId: "",
  subscriptionId: "",
  // Company fields that were being stripped before
  companyName: "Test Company",
  companyTin: "*********",
  companyCategory: "Technology",
  companyEmail: "<EMAIL>",
  companyAddress: "123 Main St",
  companyPhone: "************",
};

try {
  const result = CreateClientSchema.parse(testData);
  console.log("✅ Schema validation passed!");
  console.log("Company fields preserved:", {
    companyName: result.companyName,
    companyTin: result.companyTin,
    companyCategory: result.companyCategory,
    companyEmail: result.companyEmail,
    companyAddress: result.companyAddress,
    companyPhone: result.companyPhone,
  });
} catch (error) {
  console.log("❌ Schema validation failed:", error.message);
}
