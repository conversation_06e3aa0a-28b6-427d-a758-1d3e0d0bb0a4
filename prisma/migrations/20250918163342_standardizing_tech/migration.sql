/*
  Warnings:

  - The `status` column on the `contract` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `status` column on the `document` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `status` column on the `nest_tender` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `status` column on the `payment_method` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `status` column on the `proposal` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `status` column on the `request` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `status` column on the `role` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `status` column on the `socket` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `status` column on the `subscription` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `status` column on the `transaction` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `status` column on the `user` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- CreateEnum
CREATE TYPE "public"."Status" AS ENUM ('online', 'offline', 'active', 'inactive', 'submitted', 'received', 'negotiating', 'agreed', 'created', 'inprogress', 'reviewing', 'completed', 'closed', 'terminated', 'pending');

-- AlterTable
ALTER TABLE "public"."contract" DROP COLUMN "status",
ADD COLUMN     "status" "public"."Status" NOT NULL DEFAULT 'created';

-- AlterTable
ALTER TABLE "public"."document" DROP COLUMN "status",
ADD COLUMN     "status" "public"."Status" NOT NULL DEFAULT 'created';

-- AlterTable
ALTER TABLE "public"."nest_tender" DROP COLUMN "status",
ADD COLUMN     "status" "public"."Status" NOT NULL DEFAULT 'created';

-- AlterTable
ALTER TABLE "public"."payment_method" DROP COLUMN "status",
ADD COLUMN     "status" "public"."Status" NOT NULL DEFAULT 'created';

-- AlterTable
ALTER TABLE "public"."proposal" DROP COLUMN "status",
ADD COLUMN     "status" "public"."Status" NOT NULL DEFAULT 'created';

-- AlterTable
ALTER TABLE "public"."request" DROP COLUMN "status",
ADD COLUMN     "status" "public"."Status" NOT NULL DEFAULT 'active';

-- AlterTable
ALTER TABLE "public"."role" DROP COLUMN "status",
ADD COLUMN     "status" "public"."Status" NOT NULL DEFAULT 'created';

-- AlterTable
ALTER TABLE "public"."socket" DROP COLUMN "status",
ADD COLUMN     "status" "public"."Status";

-- AlterTable
ALTER TABLE "public"."subscription" DROP COLUMN "status",
ADD COLUMN     "status" "public"."Status" NOT NULL DEFAULT 'active';

-- AlterTable
ALTER TABLE "public"."transaction" DROP COLUMN "status",
ADD COLUMN     "status" "public"."Status" NOT NULL DEFAULT 'created';

-- AlterTable
ALTER TABLE "public"."user" DROP COLUMN "status",
ADD COLUMN     "status" "public"."Status" NOT NULL DEFAULT 'created';

-- DropEnum
DROP TYPE "public"."status";
