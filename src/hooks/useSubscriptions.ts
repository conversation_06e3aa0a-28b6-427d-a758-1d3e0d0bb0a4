"use client";

import use<PERSON><PERSON> from "swr";
import { useRouter } from "next/navigation";
import { fetcher } from "@/lib/common/requests";
import { toast } from "sonner";
import { useCallback, useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState, AppDispatch } from "@/store";
import { useSearch, type SearchConfig } from "@/lib/search";
import {
  searchSubscriptions,
  getSubscription,
  createSubscription,
  updateSubscription,
  deleteSubscription,
  performBulkAction,
  getSubscriptionStatistics,
  type CreateSubscription,
  type UpdateSubscription,
  type BulkSubscription,
  type SubscriptionSearchParams,
} from "@/store/actions/subscription";
import {
  selectCurrentSubscription,
  selectSubscriptionStatistics,
  selectIsLoading,
  selectIsCreating,
  selectIsUpdating,
  selectIsDeleting,
  selectIsBulkActionLoading,
  selectIsStatisticsLoading,
  selectError,
  selectSearchQuery,
  selectFilters,
  selectSelectedSubscriptionIds,
  clearError,
  setLoading,
  setCreating,
  setUpdating,
  setDeleting,
  setBulkActionLoading,
  setError,
  setCurrentSubscription,
  setSearchQuery,
  setFilters,
  clearFilters,
  resetState,
  setSelectedSubscriptionIds,
  toggleSubscriptionSelection,
  clearSelection,
} from "@/store/slices/subscription";
import type { Subscription } from "@/lib/api/validators/schemas/subscription";
import { usePagination } from "./usePagination";

/**
 * useSubscriptions Hook
 *
 * Provides comprehensive subscription management functionality including:
 * - CRUD operations with Redux state management
 * - SWR integration for data fetching and caching
 * - Search and filtering capabilities
 * - Bulk operations
 * - Statistics and analytics
 * - Error handling and loading states
 */
export function useSubscriptions() {
  const dispatch = useDispatch<AppDispatch>();

  // Local state for filtering and search
  const [filteredSubscriptions, setFilteredSubscriptions] = useState<
    Subscription[]
  >([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);

  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedSubscriptionForEdit, setSelectedSubscriptionForEdit] =
    useState<Subscription | null>(null);
  const [selectedSubscriptionForDelete, setSelectedSubscriptionForDelete] =
    useState<Subscription | null>(null);

  // Redux selectors
  const currentSubscription = useSelector(selectCurrentSubscription);
  const statistics = useSelector(selectSubscriptionStatistics);
  const isLoading = useSelector(selectIsLoading);
  const isCreating = useSelector(selectIsCreating);
  const isUpdating = useSelector(selectIsUpdating);
  const isDeleting = useSelector(selectIsDeleting);
  const isBulkActionLoading = useSelector(selectIsBulkActionLoading);
  const isStatisticsLoading = useSelector(selectIsStatisticsLoading);
  const error = useSelector(selectError);
  const searchQuery = useSelector(selectSearchQuery);
  const filters = useSelector(selectFilters);
  const selectedSubscriptionIds = useSelector(selectSelectedSubscriptionIds);

  // SWR for data fetching
  const {
    data: swrSubscriptions,
    error: swrError,
    mutate: mutateSubscriptions,
    isLoading: swrLoading,
  } = useSWR<any>("subscription", fetcher, {
    revalidateOnFocus: false,
    revalidateOnReconnect: true,
    dedupingInterval: 5000,
  });

  const {
    data: swrStatistics,
    error: swrStatsError,
    mutate: mutateStatistics,
    isLoading: swrStatsLoading,
  } = useSWR<any>("subscription/statistics", fetcher, {
    revalidateOnFocus: false,
    revalidateOnReconnect: true,
    dedupingInterval: 30000, // Cache stats for 30 seconds
  });

  // Pagination hook
  const paginationHook = usePagination("subscriptions");

  // Search configuration for the search library
  const searchConfig: SearchConfig<Subscription, Subscription> = {
    // Data transformation (identity function since API and UI data are the same)
    adaptApiToUI: (apiData: Subscription) => apiData,

    // Define searchable fields
    searchFields: (item: Subscription) => [
      item.name,
      item.description || "",
      item.status,
      item.features.join(" "),
    ],

    // State setters
    setFilteredData: setFilteredSubscriptions,
    setSearchTerm: (term: string) => {
      setSearchTerm(term);
      dispatch(setSearchQuery(term));
    },
    setIsSearching,

    // Redux search action
    searchAction: searchSubscriptions,

    // Current data
    currentData: swrSubscriptions?.data || [],
    loadedApiData: swrSubscriptions?.data || [],

    // Debounce configuration
    debounceDelay: 300,
  };

  // Initialize search function
  const searchFunction = useSearch(dispatch, searchConfig);

  // Apply additional filters to search results or all data
  useEffect(() => {
    // If no search term and no filters, show all data
    setFilteredSubscriptions(swrSubscriptions?.data || []);
  }, [swrSubscriptions?.data]);

  // CRUD operations
  const handleCreateSubscription = useCallback(
    async (subscriptionData: CreateSubscription) => {
      try {
        dispatch(setCreating(true));
        const result = await dispatch(
          createSubscription(subscriptionData)
        ).unwrap();

        await mutateSubscriptions(); // Refresh SWR data
        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Failed to create subscription";
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setCreating(false));
      }
    },
    [dispatch, mutateSubscriptions]
  );

  const handleUpdateSubscription = useCallback(
    async (id: string, subscriptionData: Partial<UpdateSubscription>) => {
      try {
        dispatch(setUpdating(true));
        const result = await dispatch(
          updateSubscription({ id, data: subscriptionData })
        ).unwrap();

        await mutateSubscriptions(); // Refresh SWR data
        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Failed to update subscription";
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setUpdating(false));
      }
    },
    [dispatch, mutateSubscriptions]
  );

  const handleDeleteSubscription = useCallback(
    async (id: string) => {
      try {
        dispatch(setDeleting(true));
        const result = await dispatch(deleteSubscription(id));

        await mutateSubscriptions(); // Refresh SWR data
        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Failed to delete subscription";
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setDeleting(false));
      }
    },
    [dispatch, mutateSubscriptions]
  );

  const handleGetSubscription = useCallback(
    async (id: string) => {
      try {
        dispatch(setLoading(true));
        const result = await dispatch(getSubscription(id)).unwrap();

        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to get subscription";
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setLoading(false));
      }
    },
    [dispatch]
  );

  // Search and filtering using search library
  const handleSearchSubscriptions = useCallback(
    async (params: SubscriptionSearchParams) => {
      try {
        const result = await searchFunction({
          query: params.query,
          page: params.page,
          limit: params.limit,
          ...params,
        });
        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Failed to search subscriptions";
        dispatch(setError(errorMessage));
        throw error;
      }
    },
    [searchFunction, dispatch]
  );

  const updateSearchTerm = useCallback(
    async (term: string) => {
      await searchFunction({ query: term });
    },
    [searchFunction]
  );

  const updateFilters = useCallback(
    (newFilters: Partial<typeof filters>) => {
      dispatch(setFilters(newFilters));
    },
    [dispatch]
  );

  const clearAllFilters = useCallback(async () => {
    await searchFunction({ query: "" }); // Clear search using search library
    dispatch(clearFilters());
  }, [searchFunction, dispatch]);

  // Bulk operations
  const handleBulkAction = useCallback(
    async (bulkData: BulkSubscription) => {
      try {
        dispatch(setBulkActionLoading(true));
        await dispatch(performBulkAction(bulkData)).unwrap();
        await mutateSubscriptions(); // Refresh SWR data
        dispatch(clearSelection()); // Clear selection after successful bulk action
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Failed to perform bulk action";
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setBulkActionLoading(false));
      }
    },
    [dispatch, mutateSubscriptions]
  );

  // Statistics
  const handleGetStatistics = useCallback(async () => {
    try {
      const result = await dispatch(getSubscriptionStatistics()).unwrap();
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to get statistics";
      dispatch(setError(errorMessage));
      throw error;
    }
  }, [dispatch]);

  // Utility functions
  const clearErrorState = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  const resetSubscriptionState = useCallback(() => {
    dispatch(resetState());
  }, [dispatch]);

  // Selection management
  const handleToggleSelection = useCallback(
    (id: string) => {
      dispatch(toggleSubscriptionSelection(id));
    },
    [dispatch]
  );

  const handleSetSelection = useCallback(
    (ids: string[]) => {
      dispatch(setSelectedSubscriptionIds(ids));
    },
    [dispatch]
  );

  const handleClearSelection = useCallback(() => {
    dispatch(clearSelection());
  }, [dispatch]);

  // Dialog management functions
  const openCreateDialog = useCallback(() => {
    setIsCreateDialogOpen(true);
  }, []);

  const closeCreateDialog = useCallback(() => {
    setIsCreateDialogOpen(false);
  }, []);

  const openEditDialog = useCallback((subscription: Subscription) => {
    setSelectedSubscriptionForEdit(subscription);
    setIsEditDialogOpen(true);
  }, []);

  const closeEditDialog = useCallback(() => {
    setIsEditDialogOpen(false);
    setSelectedSubscriptionForEdit(null);
  }, []);

  const openDeleteDialog = useCallback((subscription: Subscription) => {
    setSelectedSubscriptionForDelete(subscription);
    setIsDeleteDialogOpen(true);
  }, []);

  const closeDeleteDialog = useCallback(() => {
    setIsDeleteDialogOpen(false);
    setSelectedSubscriptionForDelete(null);
  }, []);

  return {
    // Data
    subscriptions: filteredSubscriptions,
    allSubscriptions: swrSubscriptions?.data || [],
    currentSubscription,
    statistics: statistics || swrStatistics?.data,

    // Loading states
    isLoading: isLoading || swrLoading,
    isCreating,
    isUpdating,
    isDeleting,
    isBulkActionLoading,
    isStatisticsLoading: isStatisticsLoading || swrStatsLoading,
    isSearching, // Add search loading state

    // Error states
    error: error || swrError?.message || swrStatsError?.message,

    // Search and filtering
    searchTerm,
    searchQuery,
    filters,
    searchFunction, // Expose search function for direct use

    // Selection
    selectedSubscriptionIds,

    // Pagination
    ...paginationHook,

    // CRUD operations
    createSubscription: handleCreateSubscription,
    updateSubscription: handleUpdateSubscription,
    deleteSubscription: handleDeleteSubscription,
    getSubscription: handleGetSubscription,

    // Search and filtering
    searchSubscriptions: handleSearchSubscriptions,
    setSearchTerm: updateSearchTerm,
    setFilters: updateFilters,
    clearFilters: clearAllFilters,

    // Bulk operations
    performBulkAction: handleBulkAction,

    // Statistics
    getStatistics: handleGetStatistics,

    // Selection management
    toggleSelection: handleToggleSelection,
    setSelection: handleSetSelection,
    clearSelection: handleClearSelection,

    // Utility functions
    clearError: clearErrorState,
    resetState: resetSubscriptionState,
    refreshData: mutateSubscriptions,
    refreshStatistics: mutateStatistics,

    // Redux actions for direct use
    setCurrentSubscription: (subscription: Subscription | null) =>
      dispatch(setCurrentSubscription(subscription)),

    // Dialog states
    isCreateDialogOpen,
    isEditDialogOpen,
    isDeleteDialogOpen,
    selectedSubscriptionForEdit,
    selectedSubscriptionForDelete,

    // Dialog management functions
    openCreateDialog,
    closeCreateDialog,
    openEditDialog,
    closeEditDialog,
    openDeleteDialog,
    closeDeleteDialog,
  };
}
