"use client";

import { useState, use<PERSON><PERSON>back, useMemo } from "react";
import { toast } from "sonner";
import useS<PERSON> from "swr";
import { api } from "@/lib/common/requests";
import {
  createSearchFunction,
  type SearchConfig,
  type SearchParams,
} from "@/lib/search";
import type {
  Role,
  CreateRoleSchema,
  UpdateRoleSchema,
} from "@/lib/api/validators/schemas/role";
import { z } from "zod";

// SWR fetcher function
const fetcher = async (url: string) => {
  const response = await api.get(url);
  if (response.success && response.data) {
    return Array.isArray(response.data)
      ? response.data
      : response.data.roles || [];
  }
  throw new Error(response.message || "Failed to fetch roles");
};

// Role filters interface
interface RoleFilters {
  status?: "created" | "active" | "inactive";
  search?: string;
}

// useRoles hook options
interface UseRolesOptions {
  page?: number;
  limit?: number;
  autoFetch?: boolean;
}

// useRoles hook return type
interface UseRolesReturn {
  // Data
  roles: Role[];
  filteredRoles: Role[];
  totalRoles: number;

  // Loading states
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  isSearching: boolean;

  // Search and filters
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  filters: RoleFilters;
  setFilters: (filters: RoleFilters) => void;
  searchRoles: (query: string) => Promise<any>;

  // CRUD operations
  createRole: (data: z.infer<typeof CreateRoleSchema>) => Promise<void>;
  updateRole: (
    id: string,
    data: Partial<z.infer<typeof UpdateRoleSchema>>
  ) => Promise<void>;
  deleteRole: (id: string) => Promise<void>;
  refreshRoles: () => Promise<void>;

  // Utility functions
  getRoleById: (id: string) => Role | undefined;
  getRolesByStatus: (status: string) => Role[];
}

export function useRoles(options: UseRolesOptions = {}): UseRolesReturn {
  const { page = 1, limit = 50, autoFetch = true } = options;

  // State management for mutations and filters
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState<RoleFilters>({});

  // Search-specific state
  const [filteredRoles, setFilteredRoles] = useState<Role[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // Build SWR key with query parameters
  const swrKey = useMemo(() => {
    if (!autoFetch) return null;

    const queryParams = new URLSearchParams();
    if (page) queryParams.append("page", page.toString());
    if (limit) queryParams.append("limit", limit.toString());
    if (filters.status) queryParams.append("status", filters.status);
    if (searchQuery) queryParams.append("search", searchQuery);

    return `roles?${queryParams.toString()}`;
  }, [autoFetch, page, limit, filters.status, searchQuery]);

  // Use SWR for data fetching
  const {
    data: roles = [],
    error,
    isLoading,
    mutate,
  } = useSWR<Role[]>(swrKey, fetcher, {
    revalidateOnFocus: false,
    revalidateOnReconnect: true,
    dedupingInterval: 5000,
  });

  // Handle SWR errors
  if (error) {
    console.error("Error fetching roles:", error);
    toast.error("Error fetching roles");
  }

  // Search configuration for the search library
  const searchConfig: SearchConfig<Role, Role> = {
    // Data transformation (identity function since API and UI data are the same)
    adaptApiToUI: (apiData: Role) => apiData,

    // Define searchable fields
    searchFields: (item: Role) => [
      item.name || "",
      item.description || "",
      item.status || "",
    ],

    // State setters
    setFilteredData: setFilteredRoles,
    setSearchTerm: setSearchQuery,
    setIsSearching,

    // Search action (fallback for server-side search if needed)
    searchAction: async (params: SearchParams) => {
      // For roles, we'll primarily rely on local search with SWR data
      // This could be extended to call a server-side search API if needed
      const queryParams = new URLSearchParams();
      if (params.query) queryParams.append("search", params.query);
      if (params.page) queryParams.append("page", params.page.toString());
      if (params.limit) queryParams.append("limit", params.limit.toString());

      const response = await api.get(`roles?${queryParams.toString()}`);
      return {
        data: Array.isArray(response.data)
          ? response.data
          : response.data?.roles || [],
        pagination: response.pagination || null,
      };
    },

    // Current data for local search
    currentData: roles || [],
    loadedApiData: roles || [],

    // Debounce configuration
    debounceDelay: 300,
  };

  // Create search function using the search library
  const { searchFunction } = createSearchFunction(null as any, searchConfig);

  // Enhanced search function that handles both local and API search
  const searchRoles = useCallback(
    async (query: string) => {
      try {
        const result = await searchFunction({
          query,
          page,
          limit,
        });
        return result;
      } catch (error) {
        console.error("Error searching roles:", error);
        toast.error("Error searching roles");
        return { success: false, error: "Search failed" };
      }
    },
    [searchFunction, page, limit]
  );

  // Create role
  const createRole = useCallback(
    async (data: z.infer<typeof CreateRoleSchema>) => {
      try {
        setIsCreating(true);

        const response = await api.post("roles", data);

        if (response.success && response.data) {
          // Optimistically update the cache
          mutate((currentRoles) => {
            if (!currentRoles) return [response.data];
            return [...currentRoles, response.data];
          }, false);
          toast.success("Role created successfully");
        } else {
          throw new Error(response.message || "Failed to create role");
        }
      } catch (error) {
        console.error("Error creating role:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Failed to create role";
        toast.error(errorMessage);
        throw error;
      } finally {
        setIsCreating(false);
      }
    },
    [mutate]
  );

  // Update role
  const updateRole = useCallback(
    async (id: string, data: Partial<z.infer<typeof UpdateRoleSchema>>) => {
      try {
        setIsUpdating(true);

        const response = await api.put(`roles/${id}`, data);

        if (response.success && response.data) {
          // Optimistically update the cache
          mutate((currentRoles) => {
            if (!currentRoles) return [];
            return currentRoles.map((role) =>
              role.id === id ? { ...role, ...response.data } : role
            );
          }, false);
          toast.success("Role updated successfully");
        } else {
          throw new Error(response.message || "Failed to update role");
        }
      } catch (error) {
        console.error("Error updating role:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Failed to update role";
        toast.error(errorMessage);
        throw error;
      } finally {
        setIsUpdating(false);
      }
    },
    [mutate]
  );

  // Delete role
  const deleteRole = useCallback(
    async (id: string) => {
      try {
        setIsDeleting(true);

        const response = await api.delete(`roles/${id}`);

        if (response.success) {
          // Optimistically update the cache
          mutate((currentRoles) => {
            if (!currentRoles) return [];
            return currentRoles.filter((role) => role.id !== id);
          }, false);
          toast.success("Role deleted successfully");
        } else {
          throw new Error(response.message || "Failed to delete role");
        }
      } catch (error) {
        console.error("Error deleting role:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Failed to delete role";
        toast.error(errorMessage);
        throw error;
      } finally {
        setIsDeleting(false);
      }
    },
    [mutate]
  );

  // Refresh roles
  const refreshRoles = useCallback(async () => {
    await mutate();
  }, [mutate]);

  // Utility functions
  const getRoleById = useCallback(
    (id: string): Role | undefined => {
      return roles.find((role) => role.id === id);
    },
    [roles]
  );

  const getRolesByStatus = useCallback(
    (status: string): Role[] => {
      return roles.filter((role) => role.status === status);
    },
    [roles]
  );

  // Determine which roles to display based on search state
  const displayRoles = useMemo(() => {
    // If there's a search query, show filtered results
    if (searchQuery && searchQuery.trim()) {
      return filteredRoles;
    }

    // If there are status filters, apply them to all roles
    if (filters.status) {
      return roles.filter((role) => role.status === filters.status);
    }

    // Otherwise, show all roles
    return roles;
  }, [searchQuery, filteredRoles, filters.status, roles]);

  return {
    // Data
    roles,
    filteredRoles: displayRoles,
    totalRoles: roles.length,

    // Loading states
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    isSearching,

    // Search and filters
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    searchRoles,

    // CRUD operations
    createRole,
    updateRole,
    deleteRole,
    refreshRoles,

    // Utility functions
    getRoleById,
    getRolesByStatus,
  };
}
