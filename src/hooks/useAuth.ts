"use client";

import { useRouter } from "next/navigation";
import { useSelector, useDispatch } from "react-redux";
import { useSession } from "next-auth/react";
import { RootState, AppDispatch } from "@/store";
import { toast } from "sonner";
import {
  refreshSession,
  loginUserWithOAuth,
  loginUserWithCredentials,
  registerUserWithCredentials,
  logoutUser,
  updateUserAvatar,
  updateUserAction,
  updatePassword,
} from "@/store/actions/auth";
import {
  selectAuth,
  selectRole,
  selectSession,
  selectUserAccount,
  selectSessionAccount,
  selectPermissions,
  selectUser,
  selectCompany,
  selectIsAuthenticated,
  selectIsLoading,
  selectIsDashboardView,
  selectIsUserRegistered,
  updateUser,
  setDashboardView,
  toggleDashboardView,
  setUserRegistered,
  resetAuthState,
} from "@/store/slices/auth";

const DASHBOARD_DIRECTORY: string = "/home";

export function useAuth() {
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const { data: nextAuthSession } = useSession();

  // Redux selectors
  const auth = useSelector(selectAuth);
  const user = useSelector(selectUser);
  const role = useSelector(selectRole);
  const session = useSelector(selectSession);
  const company = useSelector(selectCompany);
  const permissions = useSelector(selectPermissions);

  const userAccount = useSelector(selectUserAccount);
  const sessionAccount = useSelector(selectSessionAccount);

  const accountId: string = userAccount?.id || sessionAccount?.id || "";
  const dashboard_dir: string = DASHBOARD_DIRECTORY;

  // Auth state
  const { data: currentSession, update: updateCurrentSession } = useSession();
  const isAuthenticated = useSelector((state: RootState) =>
    selectIsAuthenticated(state)
  );
  const isLoading = useSelector((state: RootState) => selectIsLoading(state));
  const isDashboardView = useSelector((state: RootState) =>
    selectIsDashboardView(state)
  );
  const isUserRegistered = useSelector((state: RootState) =>
    selectIsUserRegistered(state)
  );

  const redirectToHomeView = () => {
    setDashboardViewState(true);
    router.push(dashboard_dir);
  };

  const registerUser = async (
    firstName: string,
    lastName: string,
    phone: string,
    email: string,
    password: string
  ) => {
    try {
      const result = await dispatch(
        registerUserWithCredentials({
          firstName,
          lastName,
          phone,
          email,
          password,
        })
      ).unwrap();

      // Handle toast notifications based on result
      toast.success(
        result.payload?.message ||
          "Registration successful! Complete your profile or proceed to home view."
      );
      return result;
    } catch (error: any) {
      console.error("Registration error:", error);
      toast.error(error.message);
      throw error;
    }
  };

  // Auth actions
  const loginWithOAuth = async (provider: string = "google") => {
    try {
      const result = await dispatch(loginUserWithOAuth(provider)).unwrap();

      // Handle toast notifications based on result
      toast.success(`Successfully signed in with ${provider}!`);
      redirectToHomeView();
    } catch (error) {
      console.error("Login error:", error);
      toast.error("OAuth login failed");
      throw error;
    }
  };

  const loginWithCredentials = async (email: string, password: string) => {
    try {
      await dispatch(loginUserWithCredentials({ email, password })).unwrap();

      // Handle toast notifications based on result
      toast.success("Login successful!");
      redirectToHomeView();
    } catch (error) {
      console.error("Login error:", error);
      toast.error("Login failed");
      throw error;
    }
  };

  const logout = async () => {
    try {
      const result = await dispatch(logoutUser()).unwrap();
      router.push("/");
      // Handle toast notifications based on result
      toast.success("Successfully signed out!");
      return result;
    } catch (error) {
      console.error("Logout error:", error);
      toast.error("Logout failed");
      throw error;
    }
  };

  const exitDashboardView = () => {
    setDashboardViewState(false);
    logout();
  };

  const refreshUserSession = async () => {
    if (isAuthenticated) return;

    try {
      const result = await dispatch(refreshSession());
      return result;
    } catch (error) {
      console.error("Refresh session error:", error);
      throw error;
    }
  };

  const updateAvatar = async (avatar: File) => {
    if (!user?.id) return;

    let formData = new FormData();
    formData.append("avatar", avatar, avatar.name);

    try {
      const result = await dispatch(
        updateUserAvatar({ id: user?.id, avatar: formData })
      ).unwrap();

      await updateCurrentSession({
        user: {
          ...currentSession,
          image: result.image,
        },
      });

      refreshUserSession();
      // Handle toast notifications based on result
      toast.success("Avatar updated successfully");
      return result;
    } catch (error) {
      console.error("Update avatar error:", error);
      toast.error("Failed to update avatar");
      throw error;
    }
  };

  function routeAuthenticated() {
    if (!isAuthenticated) router.push("/");

    setDashboardViewState(true);
    router.push(dashboard_dir);
  }

  // Dashboard view actions
  const setDashboardViewState = (isDashboard: boolean) => {
    dispatch(setDashboardView(isDashboard));
  };

  const toggleDashboardViewState = () => {
    dispatch(toggleDashboardView());
  };

  const isCurrentSessionYours = (id: string): boolean =>
    id ? id === accountId : false;

  const updateUserPassword = async (passwordData: {
    currentPassword: string;
    newPassword: string;
  }) => {
    try {
      const result = await dispatch(updatePassword(passwordData)).unwrap();

      // Handle toast notifications based on result
      toast.success("Password updated successfully");
      return result;
    } catch (error) {
      console.error("Update password error:", error);

      // Handle specific error messages
      let errorMessage = "Failed to update password";
      if (error && typeof error === "object" && "message" in error) {
        errorMessage = (error as any).message;
      }

      toast.error(errorMessage);
      throw error;
    }
  };

  // Update user profile
  const updateUser = async (userData: {
    firstName?: string;
    lastName?: string;
    email?: string;
    phone?: string;
  }) => {
    try {
      const result = await dispatch(updateUserAction(userData)).unwrap();

      await updateCurrentSession({
        user: {
          ...currentSession,
          ...result,
        },
      });

      refreshUserSession();
      // Handle toast notifications based on result
      toast.success("Profile updated successfully");
      return result;
    } catch (error: any) {
      console.error("Update user error:", error);

      // Handle specific error messages
      let errorMessage = "Failed to update profile";
      if (error && typeof error === "object" && "message" in error) {
        errorMessage = error.message;
      }

      toast.error(errorMessage);
      throw error;
    }
  };

  // Registration state management
  const setRegistrationState = (registered: boolean) => {
    dispatch(setUserRegistered(registered));
  };

  // Complete auth state reset
  const resetCompleteAuthState = () => {
    dispatch(resetAuthState());
  };

  return {
    // State
    auth,
    user,
    role,
    session,
    company,
    isLoading,
    accountId,
    permissions,
    isAuthenticated,
    isDashboardView,
    isUserRegistered,
    nextAuthSession,

    // Actions
    logout,
    updateAvatar,
    updateUser,
    registerUser,
    loginWithOAuth,

    redirectToHome: redirectToHomeView,
    updatePassword: updateUserPassword,
    exitDashboardView,
    routeAuthenticated,
    refreshUserSession,
    loginWithCredentials,
    setDashboardViewState,
    isCurrentSessionYours,
    toggleDashboardViewState,
    setRegistrationState,
    resetCompleteAuthState,
  };
}
