"use client";

import { use<PERSON><PERSON>back, useState, useMemo } from "react";
import useS<PERSON> from "swr";
import { toast } from "sonner";
import {
  createSearchFunction,
  type SearchConfig,
  type SearchParams,
} from "@/lib/search";

import { api } from "@/lib/common/requests";
import type {
  User,
  UserType,
  CreateClient,
  CreateEmployee,
  UpdateClient,
  UpdateEmployee,
  UpdateUser,
  BulkUser,
  UserStatistics,
} from "@/lib/api/validators/schemas/user";

// User search parameters interface
export interface UserSearchParams {
  id?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  type?: UserType;
  status?: string;
  companyId?: string;
  roleId?: string;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  includeRole?: boolean;
  includeCompany?: boolean;
}

// User filters interface
interface UserFilters {
  type?: UserType;
  status?: string;
  companyId?: string;
  roleId?: string;
}

// Hook options interface
interface UseUsersOptions {
  page?: number;
  limit?: number;
  autoFetch?: boolean;
  initialParams?: UserSearchParams;
}

// Hook interface
export interface UseUsersReturn {
  // Data (from SWR)
  users: User[];
  filteredUsers: User[];
  currentUser: User | null;
  statistics: UserStatistics | null;
  totalUsers: number;

  // Loading states (from SWR + local state)
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  isBulkActionLoading: boolean;
  isStatisticsLoading: boolean;
  isSearching: boolean;

  // Error handling (from SWR)
  error: string | null;

  // Search and filtering (local state)
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  filters: UserFilters;
  setFilters: (filters: UserFilters) => void;
  clearFilters: () => void;
  searchUsers: (query: string) => Promise<any>;

  // Pagination (local state)
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  setPagination: (pagination: any) => void;

  // Selection (local state)
  selectedUserIds: string[];
  selectedUsers: User[];
  setSelectedUserIds: (ids: string[]) => void;
  toggleUserSelection: (id: string) => void;
  clearSelection: () => void;

  // CRUD operations (API calls + SWR revalidation)
  createClient: (data: CreateClient) => Promise<void>;
  createEmployee: (data: CreateEmployee) => Promise<void>;
  updateClient: (id: string, data: Partial<UpdateClient>) => Promise<void>;
  updateEmployee: (id: string, data: Partial<UpdateEmployee>) => Promise<void>;
  updateUser: (id: string, data: Partial<UpdateUser>) => Promise<void>;
  deleteUser: (id: string) => Promise<void>;
  performBulkAction: (data: BulkUser) => Promise<void>;

  // Data fetching (SWR)
  refreshUsers: () => void;
  getUserStatistics: () => Promise<void>;
  setCurrentUser: (user: User | null) => void;
  mutateUsers: () => void;
}

// SWR fetcher function
const fetcher = async (url: string) => {
  const response = await api.get(url);
  if (response.success && response.data) {
    return Array.isArray(response.data)
      ? response.data
      : response.data.users || [];
  }
  throw new Error(response.message || "Failed to fetch users");
};

// Main hook
export const useUsers = (options: UseUsersOptions = {}): UseUsersReturn => {
  const { page = 1, limit = 10, autoFetch = true, initialParams } = options;

  // Local state
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [statistics, setStatistics] = useState<UserStatistics | null>(null);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchQuery, setSearchQuery] = useState(initialParams?.search || "");
  const [filters, setFilters] = useState<UserFilters>({
    type: initialParams?.type,
    status: initialParams?.status,
    companyId: initialParams?.companyId,
    roleId: initialParams?.roleId,
  });
  const [pagination, setPagination] = useState({
    page: initialParams?.page || page,
    limit: initialParams?.limit || limit,
    total: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  });
  const [selectedUserIds, setSelectedUserIds] = useState<string[]>([]);

  // CRUD operation loading states
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isBulkActionLoading, setIsBulkActionLoading] = useState(false);
  const [isStatisticsLoading, setIsStatisticsLoading] = useState(false);

  // Search-specific state
  const [isSearching, setIsSearching] = useState(false);

  // Build SWR keys for separate employee and client calls
  const employeeSwrKey = useMemo(() => {
    if (!autoFetch) return null;

    const params = new URLSearchParams();
    if (filters.status) params.append("status", filters.status);
    if (filters.companyId) params.append("companyId", filters.companyId);
    if (filters.roleId) params.append("roleId", filters.roleId);
    params.append("page", pagination.page.toString());
    params.append("limit", pagination.limit.toString());
    params.append("includeRole", "true");

    return `employee?${params.toString()}`;
  }, [autoFetch, filters, pagination.page, pagination.limit]);

  const clientSwrKey = useMemo(() => {
    if (!autoFetch) return null;

    const params = new URLSearchParams();
    if (filters.status) params.append("status", filters.status);
    if (filters.companyId) params.append("companyId", filters.companyId);
    if (filters.roleId) params.append("roleId", filters.roleId);
    params.append("page", pagination.page.toString());
    params.append("limit", pagination.limit.toString());
    params.append("includeCompany", "true");

    return `client?${params.toString()}`;
  }, [autoFetch, filters, pagination.page, pagination.limit]);

  // Separate SWR calls for employees and clients
  const {
    data: employees = [],
    error: employeeError,
    mutate: mutateEmployees,
    isLoading: isLoadingEmployees,
  } = useSWR<User[]>(employeeSwrKey, fetcher, {
    revalidateOnFocus: false,
    revalidateOnReconnect: true,
    dedupingInterval: 5000,
  });

  const {
    data: clients = [],
    error: clientError,
    mutate: mutateClients,
    isLoading: isLoadingClients,
  } = useSWR<User[]>(clientSwrKey, fetcher, {
    revalidateOnFocus: false,
    revalidateOnReconnect: true,
    dedupingInterval: 5000,
  });

  // Combine data and states
  const users = useMemo(() => [...employees, ...clients], [employees, clients]);
  const error = employeeError || clientError;
  const isLoading = isLoadingEmployees || isLoadingClients;

  // Combined mutate function
  const mutateUsers = useCallback(() => {
    mutateEmployees();
    mutateClients();
  }, [mutateEmployees, mutateClients]);

  // Handle SWR errors
  if (error) {
    console.error("Error fetching users:", error);
    toast.error("Error fetching users");
  }

  // Search configuration for the search library
  const searchConfig: SearchConfig<User, User> = {
    // Data transformation (identity function since API and UI data are the same)
    adaptApiToUI: (apiData: User) => apiData,

    // Define searchable fields
    searchFields: (item: User) => [
      item.firstName || "",
      item.lastName || "",
      item.email,
      item.type,
      item.status,
      item.phone || "",
    ],

    // State setters
    setFilteredData: setFilteredUsers,
    setSearchTerm: setSearchQuery,
    setIsSearching,

    // Search action (fallback for server-side search if needed)
    searchAction: async (params: SearchParams) => {
      // For users, we'll primarily rely on local search with SWR data
      // This could be extended to call a server-side search API if needed
      const queryParams = new URLSearchParams();
      if (params.query) queryParams.append("search", params.query);
      if (params.page) queryParams.append("page", params.page.toString());
      if (params.limit) queryParams.append("limit", params.limit.toString());

      // Determine API endpoint based on user type filter
      let apiEndpoint = `user?${queryParams.toString()}`;
      if (filters.type === "employee") {
        apiEndpoint = `employee?${queryParams.toString()}`;
      } else if (filters.type === "client") {
        apiEndpoint = `client?${queryParams.toString()}`;
      }

      const response = await api.get(apiEndpoint);
      return {
        data: Array.isArray(response.data)
          ? response.data
          : response.data?.users || [],
        pagination: response.pagination || null,
      };
    },

    // Current data for local search
    currentData: users || [],
    loadedApiData: users || [],

    // Debounce configuration
    debounceDelay: 300,
  };

  // Create search function using the search library
  const { searchFunction } = createSearchFunction(null as any, searchConfig);

  // Enhanced search function that handles both local and API search
  const searchUsers = useCallback(
    async (query: string) => {
      try {
        const result = await searchFunction({
          query,
          page: pagination.page,
          limit: pagination.limit,
        });
        return result;
      } catch (error) {
        console.error("Error searching users:", error);
        toast.error("Error searching users");
        return { success: false, error: "Search failed" };
      }
    },
    [searchFunction, pagination.page, pagination.limit]
  );

  // Determine which users to display based on search state
  const displayUsers = useMemo(() => {
    // If there's a search query, show filtered results
    if (searchQuery && searchQuery.trim()) {
      return filteredUsers;
    }

    // If there are filters, apply them to all users
    const filtered = users.filter((user) => {
      if (filters.type && user.type !== filters.type) return false;
      if (filters.status && user.status !== filters.status) return false;
      if (filters.companyId && user.companyId !== filters.companyId)
        return false;
      if (filters.roleId && user.roleId !== filters.roleId) return false;
      return true;
    });

    return filtered;
  }, [searchQuery, filteredUsers, filters, users]);

  // Computed values
  const selectedUsers = displayUsers.filter((user) =>
    selectedUserIds.includes(user.id)
  );

  // Helper functions for selection
  const toggleUserSelection = useCallback((id: string) => {
    setSelectedUserIds((prev) => {
      const index = prev.indexOf(id);
      if (index > -1) {
        return prev.filter((userId) => userId !== id);
      } else {
        return [...prev, id];
      }
    });
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedUserIds([]);
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({
      type: undefined,
      status: undefined,
      companyId: undefined,
      roleId: undefined,
    });
    setSearchQuery("");
  }, []);

  // CRUD operation handlers
  const handleCreateClient = useCallback(
    async (data: CreateClient) => {
      try {
        setIsCreating(true);
        const response = await api.post("client", data);

        if (response.success && response.data) {
          // Optimistically update the client cache
          mutateClients((currentClients) => {
            if (!currentClients) return [response.data];
            return [...currentClients, response.data];
          }, false);
          toast.success("Client created successfully");
        } else {
          throw new Error(response.message || "Failed to create client");
        }
      } catch (error) {
        console.error("Error creating client:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Failed to create client";
        toast.error(errorMessage);
        throw error;
      } finally {
        setIsCreating(false);
      }
    },
    [mutateClients]
  );

  const handleCreateEmployee = useCallback(
    async (data: CreateEmployee) => {
      try {
        setIsCreating(true);
        const response = await api.post("employee", data);

        if (response.success && response.data) {
          // Optimistically update the employee cache
          mutateEmployees((currentEmployees) => {
            if (!currentEmployees) return [response.data];
            return [...currentEmployees, response.data];
          }, false);
          toast.success("Employee created successfully");
        } else {
          throw new Error(response.message || "Failed to create employee");
        }
      } catch (error) {
        console.error("Error creating employee:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Failed to create employee";
        toast.error(errorMessage);
        throw error;
      } finally {
        setIsCreating(false);
      }
    },
    [mutateEmployees]
  );

  const handleUpdateClient = useCallback(
    async (id: string, data: Partial<UpdateClient>) => {
      try {
        setIsUpdating(true);
        const response = await api.put("client", { id, ...data });

        if (response.success && response.data) {
          // Optimistically update the client cache
          mutateClients((currentClients) => {
            if (!currentClients) return [];
            return currentClients.map((user) =>
              user.id === id ? { ...user, ...response.data } : user
            );
          }, false);
          toast.success("Client updated successfully");
        } else {
          throw new Error(response.message || "Failed to update client");
        }
      } catch (error) {
        console.error("Error updating client:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Failed to update client";
        toast.error(errorMessage);
        throw error;
      } finally {
        setIsUpdating(false);
      }
    },
    [mutateClients]
  );

  const handleUpdateEmployee = useCallback(
    async (id: string, data: Partial<UpdateEmployee>) => {
      try {
        setIsUpdating(true);
        const response = await api.put("employee", { id, ...data });

        if (response.success && response.data) {
          // Optimistically update the employee cache
          mutateEmployees((currentEmployees) => {
            if (!currentEmployees) return [];
            return currentEmployees.map((user) =>
              user.id === id ? { ...user, ...response.data } : user
            );
          }, false);
          toast.success("Employee updated successfully");
        } else {
          throw new Error(response.message || "Failed to update employee");
        }
      } catch (error) {
        console.error("Error updating employee:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Failed to update employee";
        toast.error(errorMessage);
        throw error;
      } finally {
        setIsUpdating(false);
      }
    },
    [mutateEmployees]
  );

  const handleUpdateUser = useCallback(
    async (id: string, data: Partial<UpdateUser>) => {
      try {
        setIsUpdating(true);
        const response = await api.put(`user/${id}`, data);

        if (response.success && response.data) {
          // Determine which cache to update based on user type
          const userType = data.type || users.find((u) => u.id === id)?.type;
          if (userType === "client") {
            mutateClients((currentClients) => {
              if (!currentClients) return [];
              return currentClients.map((user) =>
                user.id === id ? { ...user, ...response.data } : user
              );
            }, false);
          } else if (userType === "employee") {
            mutateEmployees((currentEmployees) => {
              if (!currentEmployees) return [];
              return currentEmployees.map((user) =>
                user.id === id ? { ...user, ...response.data } : user
              );
            }, false);
          }
          toast.success("User updated successfully");
        } else {
          throw new Error(response.message || "Failed to update user");
        }
      } catch (error) {
        console.error("Error updating user:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Failed to update user";
        toast.error(errorMessage);
        throw error;
      } finally {
        setIsUpdating(false);
      }
    },
    [mutateClients, mutateEmployees, users]
  );

  const handleDeleteUser = useCallback(
    async (id: string) => {
      try {
        setIsDeleting(true);
        // Determine which cache to update based on user type
        const userToDelete = users.find((u) => u.id === id);
        let response;

        if (userToDelete?.type === "client") {
          response = await api.delete(`client?id=${id}`);
        } else if (userToDelete?.type === "employee") {
          response = await api.delete(`employee?id=${id}`);
        } else {
          // Fallback to generic user endpoint
          response = await api.delete(`user?id=${id}`);
        }

        if (response?.success) {
          // Update the appropriate cache
          if (userToDelete?.type === "client") {
            mutateClients((currentClients) => {
              if (!currentClients) return [];
              return currentClients.filter((user) => user.id !== id);
            }, false);
          } else if (userToDelete?.type === "employee") {
            mutateEmployees((currentEmployees) => {
              if (!currentEmployees) return [];
              return currentEmployees.filter((user) => user.id !== id);
            }, false);
          }
          toast.success("User deleted successfully");
        } else {
          throw new Error(response?.message || "Failed to delete user");
        }
      } catch (error) {
        console.error("Error deleting user:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Failed to delete user";
        toast.error(errorMessage);
        throw error;
      } finally {
        setIsDeleting(false);
      }
    },
    [mutateClients, mutateEmployees, users]
  );

  const handlePerformBulkAction = useCallback(
    async (data: BulkUser) => {
      try {
        setIsBulkActionLoading(true);
        const response = await api.post("user/bulk", data);

        if (response.success) {
          // Refresh the entire cache after bulk operations
          mutateUsers();
          toast.success("Bulk action completed successfully");
        } else {
          throw new Error(response.message || "Failed to perform bulk action");
        }
      } catch (error) {
        console.error("Error performing bulk action:", error);
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Failed to perform bulk action";
        toast.error(errorMessage);
        throw error;
      } finally {
        setIsBulkActionLoading(false);
      }
    },
    [mutateUsers]
  );

  const handleGetUserStatistics = useCallback(async () => {
    try {
      setIsStatisticsLoading(true);
      const response = await api.get("user/statistics");

      if (response.success && response.data) {
        setStatistics(response.data);
      } else {
        throw new Error(response.message || "Failed to get user statistics");
      }
    } catch (error) {
      console.error("Error getting user statistics:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to get user statistics";
      toast.error(errorMessage);
      throw error;
    } finally {
      setIsStatisticsLoading(false);
    }
  }, []);

  const handleRefreshUsers = useCallback(() => {
    mutateUsers();
  }, [mutateUsers]);

  return {
    // Data (from SWR)
    users,
    filteredUsers: displayUsers,
    currentUser,
    statistics,
    totalUsers: users.length,

    // Loading states (from SWR + local state)
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    isBulkActionLoading,
    isStatisticsLoading,
    isSearching,

    // Error handling (from SWR)
    error: error?.message || null,

    // Search and filtering (local state)
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    clearFilters,
    searchUsers,

    // Pagination (local state)
    pagination,
    setPagination,

    // Selection (local state)
    selectedUserIds,
    selectedUsers,
    setSelectedUserIds,
    toggleUserSelection,
    clearSelection,

    // CRUD operations (API calls + SWR revalidation)
    createClient: handleCreateClient,
    createEmployee: handleCreateEmployee,
    updateClient: handleUpdateClient,
    updateEmployee: handleUpdateEmployee,
    updateUser: handleUpdateUser,
    deleteUser: handleDeleteUser,
    performBulkAction: handlePerformBulkAction,

    // Data fetching (SWR)
    refreshUsers: handleRefreshUsers,
    getUserStatistics: handleGetUserStatistics,
    setCurrentUser,
    mutateUsers,
  };
};

// Specialized hook for employees only (excluding admin)
export const useEmployees = (
  options: Omit<UseUsersOptions, "initialParams"> & {
    initialParams?: Omit<UserSearchParams, "type">;
  } = {}
) => {
  const employeeOptions: UseUsersOptions = {
    ...options,
    initialParams: {
      ...options.initialParams,
      type: "employee",
    },
  };

  const result = useUsers(employeeOptions);

  // Filter to only return employees (excluding admin)
  const employees = useMemo(() => {
    return result.users.filter((user) => user.type === "employee");
  }, [result.users]);

  const filteredEmployees = useMemo(() => {
    return result.filteredUsers.filter((user) => user.type === "employee");
  }, [result.filteredUsers]);

  return {
    ...result,
    users: employees,
    filteredUsers: filteredEmployees,
    totalUsers: employees.length,
  };
};

// Specialized hook for clients only
export const useClients = (
  options: Omit<UseUsersOptions, "initialParams"> & {
    initialParams?: Omit<UserSearchParams, "type">;
  } = {}
) => {
  const clientOptions: UseUsersOptions = {
    ...options,
    initialParams: {
      ...options.initialParams,
      type: "client",
    },
  };

  const result = useUsers(clientOptions);

  // Filter to only return clients
  const clients = useMemo(() => {
    return result.users.filter((user) => user.type === "client");
  }, [result.users]);

  const filteredClients = useMemo(() => {
    return result.filteredUsers.filter((user) => user.type === "client");
  }, [result.filteredUsers]);

  return {
    ...result,
    users: clients,
    filteredUsers: filteredClients,
    totalUsers: clients.length,
  };
};
