"use client";

import React from "react";
import {
  UserTypeWrapper,
  <PERSON><PERSON><PERSON>ly,
  <PERSON>lient<PERSON><PERSON>ly,
  AdminOnly,
  <PERSON>ploy<PERSON><PERSON><PERSON>ly,
  UserTypeConditional,
  withUserType,
} from "@/components/common/auth";

/**
 * Example component demonstrating UserTypeWrapper usage
 */
export function UserTypeWrapperExample() {
  return (
    <div className="space-y-6 p-6">
      <h1 className="text-2xl font-bold">User Type Wrapper Examples</h1>

      {/* Basic UserTypeWrapper with allowedTypes */}
      <div className="border p-4 rounded">
        <h2 className="text-lg font-semibold mb-2">Staff Only Content (Admin + Employee)</h2>
        <UserTypeWrapper allowedTypes={["admin", "employee"]}>
          <div className="bg-blue-100 p-3 rounded">
            This content is only visible to admin and employee users.
          </div>
        </UserTypeWrapper>
      </div>

      {/* StaffOnly wrapper */}
      <div className="border p-4 rounded">
        <h2 className="text-lg font-semibold mb-2">Staff Only (Shorthand)</h2>
        <StaffOnly fallback={<div className="text-gray-500">Access denied - Staff only</div>}>
          <div className="bg-green-100 p-3 rounded">
            Staff management tools and features.
          </div>
        </StaffOnly>
      </div>

      {/* ClientOnly wrapper */}
      <div className="border p-4 rounded">
        <h2 className="text-lg font-semibold mb-2">Client Only</h2>
        <ClientOnly fallback={<div className="text-gray-500">Access denied - Clients only</div>}>
          <div className="bg-purple-100 p-3 rounded">
            Client dashboard and services.
          </div>
        </ClientOnly>
      </div>

      {/* AdminOnly wrapper */}
      <div className="border p-4 rounded">
        <h2 className="text-lg font-semibold mb-2">Admin Only</h2>
        <AdminOnly fallback={<div className="text-gray-500">Access denied - Admin only</div>}>
          <div className="bg-red-100 p-3 rounded">
            System administration panel.
          </div>
        </AdminOnly>
      </div>

      {/* EmployeeOnly wrapper */}
      <div className="border p-4 rounded">
        <h2 className="text-lg font-semibold mb-2">Employee Only</h2>
        <EmployeeOnly fallback={<div className="text-gray-500">Access denied - Employee only</div>}>
          <div className="bg-yellow-100 p-3 rounded">
            Employee tools and resources.
          </div>
        </EmployeeOnly>
      </div>

      {/* UserTypeConditional with render props */}
      <div className="border p-4 rounded">
        <h2 className="text-lg font-semibold mb-2">Conditional Rendering</h2>
        <UserTypeConditional
          admin={() => (
            <div className="bg-red-100 p-3 rounded">
              Admin-specific content with full system access.
            </div>
          )}
          employee={() => (
            <div className="bg-yellow-100 p-3 rounded">
              Employee-specific content with limited access.
            </div>
          )}
          client={() => (
            <div className="bg-purple-100 p-3 rounded">
              Client-specific content and services.
            </div>
          )}
          fallback={
            <div className="text-gray-500">
              Please log in to view content.
            </div>
          }
        />
      </div>

      {/* HOC Example */}
      <div className="border p-4 rounded">
        <h2 className="text-lg font-semibold mb-2">Higher-Order Component Example</h2>
        <ProtectedAdminComponent />
      </div>
    </div>
  );
}

// Example component using HOC
const AdminComponent = () => (
  <div className="bg-red-100 p-3 rounded">
    This component is wrapped with withUserType HOC for admin-only access.
  </div>
);

const ProtectedAdminComponent = withUserType(AdminComponent, {
  adminOnly: true,
  fallback: <div className="text-gray-500">Admin access required</div>,
});

export default UserTypeWrapperExample;
