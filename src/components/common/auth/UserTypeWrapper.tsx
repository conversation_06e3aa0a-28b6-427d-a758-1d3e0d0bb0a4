"use client";

import React from "react";
import { useAuth } from "@/hooks/useAuth";

/**
 * Props for UserTypeWrapper component
 */
export interface UserTypeWrapperProps {
  children: React.ReactNode;
  allowedTypes?: ("admin" | "employee" | "client")[];
  staffOnly?: boolean;
  clientOnly?: boolean;
  adminOnly?: boolean;
  employeeOnly?: boolean;
  fallback?: React.ReactNode;
  className?: string;
}

/**
 * UserTypeWrapper - Conditionally renders content based on user type
 * 
 * @example
 * // Show content only to staff (admin or employee)
 * <UserTypeWrapper staffOnly>
 *   <AdminPanel />
 * </UserTypeWrapper>
 * 
 * @example
 * // Show content only to clients
 * <UserTypeWrapper clientOnly>
 *   <ClientDashboard />
 * </UserTypeWrapper>
 * 
 * @example
 * // Show content to specific user types
 * <UserTypeWrapper allowedTypes={["admin", "employee"]}>
 *   <ManagementTools />
 * </UserTypeWrapper>
 * 
 * @example
 * // Show content with fallback for unauthorized users
 * <UserTypeWrapper adminOnly fallback={<div>Access denied</div>}>
 *   <AdminSettings />
 * </UserTypeWrapper>
 */
export function UserTypeWrapper({
  children,
  allowedTypes,
  staffOnly = false,
  clientOnly = false,
  adminOnly = false,
  employeeOnly = false,
  fallback = null,
  className,
}: UserTypeWrapperProps) {
  const { userType, isStaff, isClient, isAdmin, isEmployee, isAuthenticated } = useAuth();

  // If user is not authenticated, don't render anything
  if (!isAuthenticated || !userType) {
    return <>{fallback}</>;
  }

  // Check permissions based on props
  let hasAccess = false;

  if (allowedTypes && allowedTypes.length > 0) {
    hasAccess = allowedTypes.includes(userType);
  } else if (staffOnly) {
    hasAccess = isStaff;
  } else if (clientOnly) {
    hasAccess = isClient;
  } else if (adminOnly) {
    hasAccess = isAdmin;
  } else if (employeeOnly) {
    hasAccess = isEmployee;
  } else {
    // If no specific restrictions, allow all authenticated users
    hasAccess = true;
  }

  if (!hasAccess) {
    return <>{fallback}</>;
  }

  return (
    <div className={className}>
      {children}
    </div>
  );
}

/**
 * Specialized wrapper for staff-only content (admin or employee)
 */
export function StaffOnly({ 
  children, 
  fallback = null,
  className 
}: { 
  children: React.ReactNode; 
  fallback?: React.ReactNode;
  className?: string;
}) {
  return (
    <UserTypeWrapper staffOnly fallback={fallback} className={className}>
      {children}
    </UserTypeWrapper>
  );
}

/**
 * Specialized wrapper for client-only content
 */
export function ClientOnly({ 
  children, 
  fallback = null,
  className 
}: { 
  children: React.ReactNode; 
  fallback?: React.ReactNode;
  className?: string;
}) {
  return (
    <UserTypeWrapper clientOnly fallback={fallback} className={className}>
      {children}
    </UserTypeWrapper>
  );
}

/**
 * Specialized wrapper for admin-only content
 */
export function AdminOnly({ 
  children, 
  fallback = null,
  className 
}: { 
  children: React.ReactNode; 
  fallback?: React.ReactNode;
  className?: string;
}) {
  return (
    <UserTypeWrapper adminOnly fallback={fallback} className={className}>
      {children}
    </UserTypeWrapper>
  );
}

/**
 * Specialized wrapper for employee-only content
 */
export function EmployeeOnly({ 
  children, 
  fallback = null,
  className 
}: { 
  children: React.ReactNode; 
  fallback?: React.ReactNode;
  className?: string;
}) {
  return (
    <UserTypeWrapper employeeOnly fallback={fallback} className={className}>
      {children}
    </UserTypeWrapper>
  );
}

/**
 * Higher-order component for user type protection
 */
export function withUserType<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  userTypeProps: Omit<UserTypeWrapperProps, "children">
) {
  const UserTypeProtectedComponent = (props: P) => {
    return (
      <UserTypeWrapper {...userTypeProps}>
        <WrappedComponent {...props} />
      </UserTypeWrapper>
    );
  };

  UserTypeProtectedComponent.displayName = `withUserType(${
    WrappedComponent.displayName || WrappedComponent.name
  })`;

  return UserTypeProtectedComponent;
}

/**
 * Conditional renderer based on user type with render props pattern
 */
export function UserTypeConditional({
  staff,
  client,
  admin,
  employee,
  fallback = null,
}: {
  staff?: () => React.ReactNode;
  client?: () => React.ReactNode;
  admin?: () => React.ReactNode;
  employee?: () => React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const { userType, isAuthenticated } = useAuth();

  if (!isAuthenticated || !userType) {
    return <>{fallback}</>;
  }

  switch (userType) {
    case "admin":
      return <>{admin ? admin() : (staff ? staff() : fallback)}</>;
    case "employee":
      return <>{employee ? employee() : (staff ? staff() : fallback)}</>;
    case "client":
      return <>{client ? client() : fallback}</>;
    default:
      return <>{fallback}</>;
  }
}
