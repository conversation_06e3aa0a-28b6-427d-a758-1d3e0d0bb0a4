"use client";

/**
 * Settings Container Component
 *
 * Main settings component with tabbed interface for Profile, RBAC, Notifications, and About.
 * This component is moved from the page component to maintain consistency with the view structure.
 */

import React from "react";
import { SettingsProvider, useSettings } from "./SettingsContext";
import {
  Tabs,
  Ta<PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@/components/common/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";
import { ProfileTab } from "./ProfileTab";
import { CompanyTab } from "./CompanyTab";
import { PasswordTab } from "./PasswordTab";

import { NotificationsTab } from "./NotificationsTab";
import { AboutTab } from "./AboutTab";
import { Info } from "lucide-react";

// Inner component that uses the settings context
function SettingsContent() {
  const { activeTab, setActiveTab } = useSettings();

  return (
    <div className="w-full flex flex-col gap-8 p-6 max-w-5xl">
      <div className="flex flex-col gap-4">
        <h1 className="text-3xl font-bold tracking-tight text-foreground">
          Settings
        </h1>
        <p className="text-muted-foreground mt-2">
          Manage your account settings, roles, and preferences.
        </p>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-8"
      >
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="company-details">Company Details</TabsTrigger>
          <TabsTrigger value="password">Password</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="about">About</TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <Card variant="ghost">
            <CardHeader className="px-0">
              <CardTitle className="flex items-center gap-2">
                Personal Info
              </CardTitle>
              <CardDescription>
                Update your photo and personal details here.
              </CardDescription>
            </CardHeader>
            <CardContent className="px-0">
              <ProfileTab />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="company-details">
          <Card variant="ghost">
            <CardHeader className="px-0">
              <CardTitle className="flex items-center gap-2">
                Company Details
              </CardTitle>
              <CardDescription>
                Update your company details here.
              </CardDescription>
            </CardHeader>
            <CardContent className="px-0">
              <CompanyTab />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="password">
          <Card variant="ghost">
            <CardHeader className="px-0">
              <CardTitle className="flex items-center gap-2">
                Password
              </CardTitle>
              <CardDescription className="px-0">
                Please enter your current password to change your password.
              </CardDescription>
            </CardHeader>
            <CardContent className="px-0">
              <PasswordTab />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <Card variant="ghost">
            <CardHeader className="px-0">
              <CardTitle className="flex items-center gap-2">
                Notifications
              </CardTitle>
              <CardDescription>
                Configure what types of alerts and notifications you want to
                receive.
              </CardDescription>
            </CardHeader>
            <CardContent className="px-0">
              <NotificationsTab />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="about">
          <Card variant="ghost">
            <CardHeader className="px-0">
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                About
              </CardTitle>
              <CardDescription>
                Information about the application and company.
              </CardDescription>
            </CardHeader>
            <CardContent className="px-0">
              <AboutTab />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Main export component that provides the settings context
export function SettingsContainer() {
  return (
    <SettingsProvider>
      <SettingsContent />
    </SettingsProvider>
  );
}
