"use client";

/**
 * Notifications Tab Component - CRUD-based Structure
 *
 * Manages user notification preferences with granular CRUD operations.
 */

import React from "react";
import { useNotifications } from "@/hooks/useNotifications";
import { <PERSON><PERSON> } from "@/components/common/ui/button";
import { Label } from "@/components/common/ui/label";
import { Checkbox } from "@/components/common/ui/checkbox";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";
import { Bell, Mail, MessageSquare, Save } from "lucide-react";
import { toast } from "sonner";
import { LottieIconPlayer, LottieIconLib } from "@/components/common/lottie";

export function NotificationsTab() {
  const {
    notificationPreferences,
    setAllNotifications,
    updateNotification,
    updateContextNotification,

    resetNotifications,
    isLoading,
    isSaving,
    getNotificationSummary,
  } = useNotifications();

  // Handle CRUD operation updates
  const handleUpdateCRUDOperation = async (
    category: "email" | "push" | "inApp",
    context: string,
    operation: "create" | "update" | "delete",
    value: boolean
  ) => {
    try {
      await updateContextNotification(category, context, operation, value);
    } catch (error) {
      console.error("Failed to update notification preference:", error);
      toast.error("Failed to update notification preference");
    }
  };

  // Handle direct boolean property updates
  const handleUpdateDirectProperty = async (
    category: "email" | "push" | "inApp",
    key: string,
    value: boolean
  ) => {
    try {
      await updateNotification(category, key, value);
    } catch (error) {
      console.error("Failed to update notification preference:", error);
      toast.error("Failed to update notification preference");
    }
  };

  const handleResetToDefaults = async () => {
    try {
      await resetNotifications();
      toast.success("Notification preferences reset to defaults");
    } catch (error) {
      console.error("Failed to reset preferences:", error);
      toast.error("Failed to reset preferences");
    }
  };

  const handleSave = async () => {
    try {
      if (notificationPreferences) {
        await setAllNotifications(notificationPreferences);
        toast.success("Notification preferences saved successfully");
      }
    } catch (error) {
      console.error("Failed to save preferences:", error);
      toast.error("Failed to save preferences");
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  // Context-based notification types with CRUD operations
  const contextTypes = [
    {
      key: "room",
      label: "Room Management",
      description: "Notifications for room creation, updates, and deletion",
      icon: LottieIconLib.home,
    },
    {
      key: "chat",
      label: "Chat Messages",
      description: "Notifications for chat messages and conversations",
      icon: LottieIconLib.chat,
    },
    {
      key: "proposal",
      label: "Proposals",
      description: "Notifications for proposal creation, updates, and changes",
      icon: LottieIconLib.file,
    },

    {
      key: "contract",
      label: "Contracts",
      description: "Notifications for contract creation, updates, and changes",
      icon: LottieIconLib.contract,
    },
  ];

  // Direct boolean properties
  const directProperties = [
    {
      key: "systemAlerts",
      label: "System Alerts",
      description: "Important system notifications and maintenance alerts",
      icon: LottieIconLib.electric,
      categories: ["email", "push", "inApp"] as const,
    },
    {
      key: "weeklyDigest",
      label: "Weekly Digest",
      description: "Weekly summary of activities and updates",
      icon: LottieIconLib.sendEmail,
      categories: ["email"] as const,
    },
    {
      key: "roleChanges",
      label: "Role Changes",
      description: "Notifications when user roles and permissions change",
      icon: LottieIconLib.shield,
      categories: ["inApp"] as const,
    },
  ];

  const summary = getNotificationSummary();

  const renderCRUDCheckboxes = (
    category: "email" | "push" | "inApp",
    context: string
  ) => {
    const contextSettings = (notificationPreferences?.[category] as any)?.[
      context
    ];

    if (!contextSettings || typeof contextSettings !== "object") {
      return null;
    }

    return (
      <div className="flex items-center gap-8 ml-auto">
        <div className="flex items-center gap-2">
          <LottieIconPlayer
            icon={LottieIconLib.add}
            size={12}
            className="text-green-600"
          />
          <Label
            htmlFor={`${category}-${context}-create`}
            className="text-xs text-zinc-400"
          >
            Create
          </Label>
          <Checkbox
            id={`${category}-${context}-create`}
            checked={contextSettings.create || false}
            onCheckedChange={(checked) =>
              handleUpdateCRUDOperation(category, context, "create", !!checked)
            }
          />
        </div>
        <div className="flex items-center gap-2">
          <LottieIconPlayer
            icon={LottieIconLib.edit}
            size={12}
            className="text-blue-600"
          />
          <Label
            htmlFor={`${category}-${context}-update`}
            className="text-xs text-zinc-400"
          >
            Update
          </Label>
          <Checkbox
            id={`${category}-${context}-update`}
            checked={contextSettings.update || false}
            onCheckedChange={(checked) =>
              handleUpdateCRUDOperation(category, context, "update", !!checked)
            }
          />
        </div>
        <div className="flex items-center gap-2">
          <LottieIconPlayer
            icon={LottieIconLib.delete}
            size={12}
            className="text-red-600"
          />
          <Label
            htmlFor={`${category}-${context}-delete`}
            className="text-xs text-zinc-400"
          >
            Delete
          </Label>
          <Checkbox
            id={`${category}-${context}-delete`}
            checked={contextSettings.delete || false}
            onCheckedChange={(checked) =>
              handleUpdateCRUDOperation(category, context, "delete", !!checked)
            }
          />
        </div>
      </div>
    );
  };

  const RenderNotificationProperty = ({
    id,
    icon,
    type = "email",
    label,
    description,
    isDirectProperty = false,
  }: {
    id: string;
    type: "email" | "inApp" | "push";
    icon: React.ComponentType<{ className?: string }> | LottieIconLib;
    label: string;
    description: string;
    isDirectProperty?: boolean;
  }) => {
    const renderControls = () => {
      if (isDirectProperty) {
        // Handle direct boolean properties (systemAlerts, weeklyDigest, roleChanges)
        const value = (notificationPreferences?.[type] as any)?.[id];

        if (typeof value !== "boolean") {
          return null;
        }

        return (
          <Checkbox
            checked={value}
            onCheckedChange={(checked) =>
              handleUpdateDirectProperty(type, id, !!checked)
            }
          />
        );
      } else {
        // Handle CRUD operations for context-based notifications
        return renderCRUDCheckboxes(type, id);
      }
    };

    const renderIcon = () => {
      // Check if it's a Lottie icon (enum value)
      if (
        typeof icon === "string" &&
        Object.values(LottieIconLib).includes(icon as LottieIconLib)
      ) {
        return (
          <LottieIconPlayer
            icon={icon as LottieIconLib}
            size={16}
            className="text-gray-500"
          />
        );
      }
      // Otherwise, it's a Lucide icon component
      const IconComponent = icon as React.ComponentType<{ className?: string }>;
      return <IconComponent className="h-4 w-4 text-gray-500" />;
    };

    return (
      <div className="flex items-center justify-between pb-4 border-border border-b last:border-none last:pb-0">
        <div className="flex items-center gap-3">
          {renderIcon()}
          <div className="space-y-2">
            <Label className="font-medium">{label}</Label>
            <p className="text-xs text-gray-500">{description}</p>
          </div>
        </div>
        {renderControls()}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <p className="text-xs text-gray-500">
          {summary.enabled} of {summary.total} notifications enabled (
          {summary.enabledPercentage}%)
        </p>

        <div className="flex gap-2">
          <Button variant="outline" onClick={handleResetToDefaults}>
            Reset to Defaults
          </Button>
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Email Notifications */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            <CardTitle>Email Notifications</CardTitle>
          </div>
          <CardDescription>Receive notifications via email</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Context-based notifications */}
          {contextTypes.map((settings: any) => {
            const { key, ...settingsWithoutKey } = settings;
            return (
              <RenderNotificationProperty
                key={key}
                {...settingsWithoutKey}
                id={key}
                type="email"
              />
            );
          })}

          {/* Direct properties */}
          {directProperties.map((settings: any) => {
            let { categories, key, ...withOutCategorySettings } = settings;
            if (!settings.categories.includes("email")) return null;

            return (
              <RenderNotificationProperty
                key={key}
                {...withOutCategorySettings}
                id={key}
                type="email"
                isDirectProperty={true}
              />
            );
          })}
        </CardContent>
      </Card>

      {/* Push Notifications */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            <CardTitle>Push Notifications</CardTitle>
          </div>
          <CardDescription>Receive browser push notifications</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Context-based notifications */}
          {contextTypes.map((settings: any) => {
            const { key, ...settingsWithoutKey } = settings;
            return (
              <RenderNotificationProperty
                key={key}
                {...settingsWithoutKey}
                id={key}
                type="push"
              />
            );
          })}

          {/* Direct properties */}
          {directProperties.map((settings: any) => {
            let { categories, key, ...withOutCategorySettings } = settings;
            if (!settings.categories.includes("push")) return null;

            return (
              <RenderNotificationProperty
                key={key}
                {...withOutCategorySettings}
                id={key}
                type="push"
                isDirectProperty={true}
              />
            );
          })}
        </CardContent>
      </Card>

      {/* In-App Notifications */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            <CardTitle>In-App Notifications</CardTitle>
          </div>
          <CardDescription>
            Receive toast notifications while using the app
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Context-based notifications */}
          {contextTypes.map((settings: any) => {
            const { key, ...settingsWithoutKey } = settings;
            return (
              <RenderNotificationProperty
                key={key}
                {...settingsWithoutKey}
                id={key}
                type="inApp"
              />
            );
          })}

          {/* Direct properties */}
          {directProperties.map((settings: any) => {
            let { categories, key, ...withOutCategorySettings } = settings;
            if (!settings.categories.includes("inApp")) return null;

            return (
              <RenderNotificationProperty
                key={key}
                {...withOutCategorySettings}
                id={key}
                type="inApp"
                isDirectProperty={true}
              />
            );
          })}
        </CardContent>
      </Card>
    </div>
  );
}
