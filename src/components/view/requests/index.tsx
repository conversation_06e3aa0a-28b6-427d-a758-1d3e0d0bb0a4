"use client";

import React, { useState, useMemo } from "react";
import { Listing } from "@/layouts/dashboard/details/basic";
import { Button } from "@/components/common/ui/button";
import { Badge } from "@/components/common/ui/badge";
import { Clock, CheckCircle, AlertCircle, Activity, Plus } from "lucide-react";
import { useRequest } from "@/hooks/useRequest";
import type { Request } from "@/lib/api/validators/schemas/request";

export function RequestsContainer() {
  // Filter state
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  // Use request hook for requests data
  const {
    requests,
    isLoading: requestsLoading,
    error: requestsError,
    pagination: requestPagination,
    refreshData: refreshRequests,
  } = useRequest();

  // Filter requests based on search term and status
  const filteredRequests = useMemo(() => {
    let filtered = requests;

    // Apply search filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (request) =>
          request.tender_id.toLowerCase().includes(searchLower) ||
          request.message?.toLowerCase().includes(searchLower) ||
          request.user?.email?.toLowerCase().includes(searchLower) ||
          request.user?.firstName?.toLowerCase().includes(searchLower) ||
          request.user?.lastName?.toLowerCase().includes(searchLower)
      );
    }

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter((request) => request.status === statusFilter);
    }

    return filtered;
  }, [requests, searchTerm, statusFilter]);

  // Handle refresh action
  const handleRefresh = () => {
    refreshRequests();
  };

  // Calculate request statistics from the data
  const requestStats = useMemo(() => {
    const totalRequests = requests.length;
    const activeRequests = requests.filter((r) => r.status === "active").length;
    const submittedRequests = requests.filter(
      (r) => r.status === "submitted"
    ).length;
    const completedRequests = requests.filter(
      (r) => r.status === "completed"
    ).length;

    return [
      {
        name: "Total Requests",
        value: totalRequests,
        valueType: "number" as const,
        caption: "All time requests",
        color: "blue" as const,
      },
      {
        name: "Active Requests",
        value: activeRequests,
        valueType: "number" as const,
        caption: "Currently active",
        color: "green" as const,
      },
      {
        name: "Submitted Requests",
        value: submittedRequests,
        valueType: "number" as const,
        caption: "Awaiting processing",
        color: "amber" as const,
      },
      {
        name: "Completed Requests",
        value: completedRequests,
        valueType: "number" as const,
        caption: "Successfully completed",
        color: "primary" as const,
      },
    ];
  }, [requests]);

  // Request table columns
  const requestColumns = [
    {
      key: "tender_id",
      label: "Tender ID",
      render: (request: Request) => (
        <div className="flex flex-col">
          <p className="font-medium text-sm">{request.tender_id}</p>
          {request.message && (
            <p className="text-xs text-muted-foreground line-clamp-1 mt-1">
              {request.message}
            </p>
          )}
        </div>
      ),
    },
    {
      key: "user",
      label: "Requested By",
      render: (request: Request) => (
        <div className="flex flex-col">
          <p className="font-medium text-sm">
            {request.user?.firstName && request.user?.lastName
              ? `${request.user.firstName} ${request.user.lastName}`
              : request.user?.email || "Unknown User"}
          </p>
          {request.user?.email &&
            (request.user?.firstName || request.user?.lastName) && (
              <p className="text-xs text-muted-foreground">
                {request.user.email}
              </p>
            )}
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (request: Request) => (
        <Badge
          variant={
            request.status === "active" || request.status === "submitted"
              ? "default"
              : request.status === "completed" || request.status === "closed"
              ? "secondary"
              : "outline"
          }
          className="capitalize"
        >
          {request.status === "active" || request.status === "submitted" ? (
            <CheckCircle className="h-3 w-3 mr-1" />
          ) : request.status === "completed" || request.status === "closed" ? (
            <CheckCircle className="h-3 w-3 mr-1" />
          ) : (
            <AlertCircle className="h-3 w-3 mr-1" />
          )}
          {request.status}
        </Badge>
      ),
    },
    {
      key: "createdAt",
      label: "Created",
      render: (request: Request) => (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Clock className="h-4 w-4" />
          {new Date(request.createdAt).toLocaleDateString()}
        </div>
      ),
    },
  ];

  return (
    <Listing className="p-6 space-y-6">
      {/* Dashboard Header */}
      <Listing.Header
        title={{ text: "Do it for Me", size: "2xl" }}
        caption="Manage and tender bid requests"
      />

      {/* Statistics Cards */}
      <Listing.Statistics columns="grid-cols-4">
        {requestStats.map((stat, index) => (
          <Listing.StatCard
            key={index}
            name={stat.name}
            value={stat.value}
            valueType={stat.valueType}
            caption={stat.caption}
            color={stat.color}
          />
        ))}
      </Listing.Statistics>

      {/* Recent Tenders */}
      <Listing className="space-y-1">
        <Listing.Header title={{ text: "Request Queue", size: "md" }} />

        {/* Filters */}
        <Listing.Filters
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          onRefresh={handleRefresh}
          loading={requestsLoading}
          customActions={
            <div className="flex items-center gap-2">
              <Button
                variant={statusFilter === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => setStatusFilter("all")}
              >
                All
              </Button>
              <Button
                variant={statusFilter === "active" ? "default" : "outline"}
                size="sm"
                onClick={() => setStatusFilter("active")}
              >
                Active
              </Button>
              <Button
                variant={statusFilter === "submitted" ? "default" : "outline"}
                size="sm"
                onClick={() => setStatusFilter("submitted")}
              >
                Submitted
              </Button>
              <Button
                variant={statusFilter === "completed" ? "default" : "outline"}
                size="sm"
                onClick={() => setStatusFilter("completed")}
              >
                Completed
              </Button>
            </div>
          }
        />
        {/* Main Content Grid */}
        <Listing.Table
          data={filteredRequests}
          columns={requestColumns}
          loading={requestsLoading}
          emptyState={
            <div className="text-center py-8">
              <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {requestsError
                  ? "Failed to load requests"
                  : searchTerm || statusFilter !== "all"
                  ? "No requests match your filters"
                  : "No recent requests"}
              </p>
              {requestsError ? (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  className="mt-2"
                >
                  Try Again
                </Button>
              ) : searchTerm || statusFilter !== "all" ? (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSearchTerm("");
                    setStatusFilter("all");
                  }}
                  className="mt-2"
                >
                  Clear Filters
                </Button>
              ) : null}
            </div>
          }
        />

        {/* Pagination for Requests */}
        {requestPagination && requestPagination.totalPages > 1 && (
          <div className="flex justify-between items-center mt-4 pt-4 border-t">
            <span className="text-sm text-muted-foreground">
              Showing {filteredRequests.length} of {requestPagination.total}{" "}
              requests
              {searchTerm && ` (filtered by "${searchTerm}")`}
            </span>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Handle previous page - would need to implement pagination in useRequest hook
                  console.log("Previous page");
                }}
                disabled={!requestPagination.hasPreviousPage || requestsLoading}
              >
                Previous
              </Button>
              <span className="px-3 py-1 text-sm">
                Page {requestPagination.page} of {requestPagination.totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Handle next page - would need to implement pagination in useRequest hook
                  console.log("Next page");
                }}
                disabled={!requestPagination.hasNextPage || requestsLoading}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </Listing>
    </Listing>
  );
}
