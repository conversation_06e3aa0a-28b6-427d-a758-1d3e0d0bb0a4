"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/common/ui/dialog";
import { <PERSON><PERSON> } from "@/components/common/ui/button";

import { AlertTriangle, Package } from "lucide-react";
import { CreateSubscriptionForm } from "./forms/create";
import { EditSubscriptionForm } from "./forms/edit";
import { DeleteSubscriptionForm } from "./forms/delete";
import type {
  Subscription,
  CreateSubscription,
  UpdateSubscription,
} from "@/lib/api/validators/schemas/subscription";

// Create Subscription Dialog Props
interface CreateSubscriptionDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: CreateSubscription) => Promise<void>;
  isCreating: boolean;
}

// Edit Subscription Dialog Props
interface EditSubscriptionDialogProps {
  subscription: Subscription | null;
  isOpen: boolean;
  onSubmit: (id: string, data: <PERSON>ial<UpdateSubscription>) => Promise<void>;
  onOpenChange: (open: boolean) => void;
  isUpdating: boolean;
}

// Delete Subscription Dialog Props
interface DeleteSubscriptionDialogProps {
  subscription: Subscription | null;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (id: string) => Promise<void>;
  isDeleting: boolean;
}

// Create Subscription Dialog Component
export function CreateSubscriptionDialog({
  isOpen,
  onOpenChange,
  onSubmit,
  isCreating,
}: CreateSubscriptionDialogProps) {
  const handleSubmit = async (data: CreateSubscription) => {
    try {
      await onSubmit(data);
      onOpenChange(false); // Close dialog on success
    } catch (error) {
      // Error handling is done in the parent component
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Create New Subscription
          </DialogTitle>
        </DialogHeader>

        <CreateSubscriptionForm onSubmit={handleSubmit}>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isCreating}>
              {isCreating ? "Creating..." : "Create Subscription"}
            </Button>
          </DialogFooter>
        </CreateSubscriptionForm>
      </DialogContent>
    </Dialog>
  );
}

// Edit Subscription Dialog Component
export function EditSubscriptionDialog({
  subscription,
  isOpen,
  onOpenChange,
  onSubmit,
  isUpdating,
}: EditSubscriptionDialogProps) {
  if (!subscription) return null;

  const handleSubmit = async (
    id: string,
    data: Partial<UpdateSubscription>
  ) => {
    try {
      await onSubmit(id, data);
      onOpenChange(false); // Close dialog on success
    } catch (error) {
      // Error handling is done in the parent component
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Edit Subscription: {subscription.name}
          </DialogTitle>
        </DialogHeader>

        <EditSubscriptionForm
          subscription={subscription}
          onSubmit={handleSubmit}
        >
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isUpdating}>
              {isUpdating ? "Updating..." : "Update Subscription"}
            </Button>
          </DialogFooter>
        </EditSubscriptionForm>
      </DialogContent>
    </Dialog>
  );
}

// Delete Subscription Dialog Component
export function DeleteSubscriptionDialog({
  subscription,
  isOpen,
  onOpenChange,
  onConfirm,
  isDeleting,
}: DeleteSubscriptionDialogProps) {
  if (!subscription) return null;

  const handleConfirm = async () => {
    try {
      await onConfirm(subscription.id);
      onOpenChange(false);
    } catch (error) {
      // Error handling is done in the parent component
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
            Delete Subscription: {subscription.name}
          </DialogTitle>
        </DialogHeader>

        <DeleteSubscriptionForm
          subscription={subscription}
          onSuccess={() => onOpenChange(false)}
        >
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete Subscription"}
            </Button>
          </DialogFooter>
        </DeleteSubscriptionForm>
      </DialogContent>
    </Dialog>
  );
}
