"use client";

import React from "react";

import { AlertTriangle } from "lucide-react";

import type { Subscription } from "@/lib/api/validators/schemas/subscription";

// Delete Subscription Form Props
interface DeleteSubscriptionFormProps {
  subscription: Subscription;
  children?: React.ReactNode;
  onSuccess?: () => void;
}

// Delete Subscription Form Component
export function DeleteSubscriptionForm({
  subscription,
  children,
  onSuccess,
}: DeleteSubscriptionFormProps) {
  return (
    <div className="space-y-4">
      <div className="space-y-4">
        <p className="text-sm text-muted-foreground">
          Are you sure you want to delete the subscription "{subscription.name}
          "? This action cannot be undone and will permanently remove the
          subscription and all associated data.
        </p>

        {subscription.subscribers > 0 && (
          <div className="bg-amber-50 border border-amber-200 rounded-md p-3">
            <div className="flex items-center gap-2 text-amber-800">
              <AlertTriangle className="h-4 w-4" />
              <span className="text-sm font-medium">
                Warning: This subscription has {subscription.subscribers} active
                subscribers.
              </span>
            </div>
          </div>
        )}

        <div className="bg-muted p-3 rounded-md">
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Name:</span>
              <span className="text-foreground font-medium">
                {subscription.name}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Price:</span>
              <span className="text-foreground font-medium">
                ${subscription.price.toFixed(2)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Subscribers:</span>
              <span className="text-foreground font-medium">
                {subscription.subscribers}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Status:</span>
              <span className="text-foreground font-medium capitalize">
                {subscription.status}
              </span>
            </div>
          </div>
        </div>
      </div>

      {children}
    </div>
  );
}
