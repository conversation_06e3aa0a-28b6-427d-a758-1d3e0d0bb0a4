"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import { Textarea } from "@/components/common/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";
import { Plus, Trash2 } from "lucide-react";
import { toast } from "sonner";
import type { CreateSubscription } from "@/lib/api/validators/schemas/subscription";
import { useSubscriptions } from "@/hooks/useSubscriptions";

// Create Subscription Form Props
interface CreateSubscriptionFormProps {
  children?: React.ReactNode;
  onSubmit: (data: CreateSubscription) => Promise<void>;
}

// Initial form data for create
const INITIAL_CREATE_FORM_DATA: CreateSubscription = {
  name: "",
  description: "",
  status: "active",
  features: [],
  price: 0,
  subscribers: 0,
};

// Create Subscription Form Component
export function CreateSubscriptionForm({
  children,
  onSubmit,
}: CreateSubscriptionFormProps) {
  const [formData, setFormData] = useState<CreateSubscription>(
    INITIAL_CREATE_FORM_DATA
  );

  const [newFeature, setNewFeature] = useState("");

  const handleInputChange = (field: keyof CreateSubscription, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleAddFeature = () => {
    if (newFeature.trim()) {
      setFormData((prev) => ({
        ...prev,
        features: [...prev.features, newFeature.trim()],
      }));
      setNewFeature("");
    }
  };

  const handleRemoveFeature = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formData.name.trim()) {
      toast.error("Subscription name is required");
      return;
    }

    if (formData.price < 0) {
      toast.error("Price must be non-negative");
      return;
    }

    try {
      await onSubmit(formData);
      // Reset form after successful submission
      setFormData(INITIAL_CREATE_FORM_DATA);
      setNewFeature("");
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="name">Subscription Name *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => handleInputChange("name", e.target.value)}
            placeholder="Enter subscription name"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={formData.description || ""}
            onChange={(e) => handleInputChange("description", e.target.value)}
            placeholder="Enter subscription description"
            rows={3}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="price">Price ($) *</Label>
            <Input
              id="price"
              type="number"
              min="0"
              step="0.01"
              value={formData.price}
              onChange={(e) =>
                handleInputChange("price", parseFloat(e.target.value) || 0)
              }
              placeholder="0.00"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={formData.status}
              onValueChange={(value) => handleInputChange("status", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="created">Created</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="space-y-4">
        <Label>Features</Label>
        <div className="flex gap-2">
          <Input
            value={newFeature}
            onChange={(e) => setNewFeature(e.target.value)}
            placeholder="Add a feature"
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                e.preventDefault();
                handleAddFeature();
              }
            }}
          />
          <Button
            type="button"
            variant="outline"
            onClick={handleAddFeature}
            disabled={!newFeature.trim()}
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>

        {formData.features.length > 0 && (
          <div className="space-y-2">
            {formData.features.map((feature, index) => (
              <div
                key={index}
                className="flex items-center justify-between bg-muted text-foreground px-4 py-2 rounded"
              >
                <span className="text-sm">{feature}</span>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveFeature(index)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        )}
      </div>

      {children}
    </form>
  );
}
