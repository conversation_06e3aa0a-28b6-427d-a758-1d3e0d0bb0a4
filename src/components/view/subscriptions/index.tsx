"use client";

import React, { useState, useMemo, useCallback } from "react";
import { Listing } from "@/layouts/dashboard/details/basic";
import { But<PERSON> } from "@/components/common/ui/button";
import { Badge } from "@/components/common/ui/badge";
import {
  Plus,
  Clock,
  CheckCircle,
  AlertCircle,
  DollarSign,
  Users,
  Package,
} from "lucide-react";
import { useSubscriptions } from "@/hooks/useSubscriptions";
import type { Subscription } from "@/lib/api/validators/schemas/subscription";
import {
  CreateSubscriptionDialog,
  EditSubscriptionDialog,
  DeleteSubscriptionDialog,
} from "./dialogs";
import { SubscriptionActionsDropdown } from "./subscription-actions-dropdown";

export function SubscriptionContainer() {
  // Use the subscriptions hook
  const {
    subscriptions,
    statistics,
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    error,
    searchTerm,
    setSearchTerm,
    selectedSubscriptionIds,
    setSelection,
    clearSelection,
    createSubscription,
    updateSubscription,
    deleteSubscription,
    refreshData,
    refreshStatistics,
    // Dialog states
    isCreateDialogOpen,
    isEditDialogOpen,
    isDeleteDialogOpen,
    selectedSubscriptionForEdit,
    selectedSubscriptionForDelete,
    // Dialog management functions
    openCreateDialog,
    closeCreateDialog,
    openEditDialog,
    closeEditDialog,
    openDeleteDialog,
    closeDeleteDialog,
  } = useSubscriptions();

  // Local state for UI
  const [statusFilter, setStatusFilter] = useState("all");

  // Handle refresh action
  const handleRefresh = useCallback(() => {
    refreshData();
    refreshStatistics();
  }, [refreshData, refreshStatistics]);

  // Handle subscription creation
  const handleCreateSubscription = useCallback(() => {
    openCreateDialog();
  }, [openCreateDialog]);

  // Filter subscriptions based on status filter
  const filteredSubscriptions = useMemo(() => {
    if (statusFilter === "all") return subscriptions;
    return subscriptions.filter(
      (subscription) => subscription.status === statusFilter
    );
  }, [subscriptions, statusFilter]);

  // Calculate statistics from the hook data
  const subscriptionStats = useMemo(() => {
    if (statistics) {
      return [
        {
          name: "Total Subscriptions",
          value: statistics.totalSubscriptions,
          valueType: "number" as const,
          caption: "All subscription plans",
          color: "blue" as const,
        },
        {
          name: "Active Subscriptions",
          value: statistics.activeSubscriptions,
          valueType: "number" as const,
          caption: "Currently active",
          color: "green" as const,
        },
        {
          name: "Total Subscribers",
          value: statistics.totalSubscribers,
          valueType: "number" as const,
          caption: "Across all plans",
          color: "primary" as const,
        },
        {
          name: "Average Price",
          value: statistics.averagePrice,
          valueType: "dollar" as const,
          caption: "Per subscription",
          color: "amber" as const,
        },
      ];
    }
    return [];
  }, [statistics]);

  // Subscription table columns
  const subscriptionColumns = [
    {
      key: "name",
      label: "Subscription Plan",
      render: (subscription: Subscription) => (
        <div className="flex items-center gap-3">
          <Package className="h-4 w-4 text-muted-foreground" />
          <div>
            <p className="font-medium">{subscription.name}</p>
            {subscription.description && (
              <p className="text-sm text-muted-foreground line-clamp-1">
                {subscription.description}
              </p>
            )}
          </div>
        </div>
      ),
    },
    {
      key: "price",
      label: "Price",
      render: (subscription: Subscription) => (
        <div className="flex items-center gap-2">
          <DollarSign className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium">
            {subscription.price.toLocaleString()}
          </span>
        </div>
      ),
    },
    {
      key: "subscribers",
      label: "Subscribers",
      render: (subscription: Subscription) => (
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-muted-foreground" />
          <span>{subscription.subscribers}</span>
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (subscription: Subscription) => (
        <Badge
          variant={subscription.status === "active" ? "default" : "secondary"}
          className="capitalize"
        >
          {subscription.status === "active" ? (
            <CheckCircle className="h-3 w-3 mr-1" />
          ) : (
            <AlertCircle className="h-3 w-3 mr-1" />
          )}
          {subscription.status}
        </Badge>
      ),
    },
    {
      key: "createdAt",
      label: "Created",
      render: (subscription: Subscription) => (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Clock className="h-4 w-4" />
          {new Date(subscription.createdAt).toLocaleDateString()}
        </div>
      ),
    },
    {
      key: "actions",
      label: "Actions",
      render: (subscription: Subscription) => (
        <SubscriptionActionsDropdown
          subscription={subscription}
          onEdit={openEditDialog}
          onDelete={openDeleteDialog}
          isDeleting={isDeleting}
        />
      ),
    },
  ];

  return (
    <Listing className="p-6 space-y-6">
      {/* Dashboard Header */}
      <Listing.Header
        title={{ text: "Subscriptions", size: "2xl" }}
        caption="Manage subscription plans and pricing"
        actions={
          <Button onClick={handleCreateSubscription} disabled={isCreating}>
            <Plus size={16} className="mr-2" />
            {isCreating ? "Creating..." : "New Subscription"}
          </Button>
        }
      />

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex items-center gap-2 text-red-800">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm font-medium">Error: {error}</span>
          </div>
        </div>
      )}

      {/* Statistics Cards */}
      <Listing.Statistics columns="grid-cols-4">
        {subscriptionStats.map((stat, index) => (
          <Listing.StatCard
            key={index}
            name={stat.name}
            value={stat.value}
            valueType={stat.valueType}
            caption={stat.caption}
            color={stat.color}
            loading={!statistics}
          />
        ))}
      </Listing.Statistics>

      {/* Subscriptions Table */}
      <Listing className="space-y-1">
        <Listing.Header title={{ text: "Subscription Plans", size: "md" }} />

        {/* Filters */}
        <Listing.Filters
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          onRefresh={handleRefresh}
          loading={isLoading}
          customActions={
            <div className="flex items-center gap-2">
              <Button
                variant={statusFilter === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => setStatusFilter("all")}
              >
                All
              </Button>
              <Button
                variant={statusFilter === "active" ? "default" : "outline"}
                size="sm"
                onClick={() => setStatusFilter("active")}
              >
                Active
              </Button>
              <Button
                variant={statusFilter === "inactive" ? "default" : "outline"}
                size="sm"
                onClick={() => setStatusFilter("inactive")}
              >
                Inactive
              </Button>
              {selectedSubscriptionIds.length > 0 && (
                <Button variant="outline" size="sm" onClick={clearSelection}>
                  Clear Selection ({selectedSubscriptionIds.length})
                </Button>
              )}
            </div>
          }
        />

        {/* Main Content Table */}
        <Listing.Table
          data={filteredSubscriptions}
          columns={subscriptionColumns}
          loading={isLoading}
          enableCheckboxes={true}
          selectedRowIds={selectedSubscriptionIds}
          onSelectionChange={setSelection}
          getRowId={(subscription) => subscription.id}
          emptyState={
            <div className="text-center py-8">
              <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {error
                  ? "Failed to load subscriptions"
                  : searchTerm || statusFilter !== "all"
                  ? "No subscriptions match your filters"
                  : "No subscription plans found"}
              </p>
              {error ? (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  className="mt-2"
                >
                  Try Again
                </Button>
              ) : searchTerm || statusFilter !== "all" ? (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSearchTerm("");
                    setStatusFilter("all");
                  }}
                  className="mt-2"
                >
                  Clear Filters
                </Button>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCreateSubscription}
                  className="mt-2"
                >
                  <Plus size={16} className="mr-2" />
                  Create First Subscription
                </Button>
              )}
            </div>
          }
        />
      </Listing>

      {/* Dialogs */}
      <CreateSubscriptionDialog
        isOpen={isCreateDialogOpen}
        onOpenChange={closeCreateDialog}
        onSubmit={createSubscription}
        isCreating={isCreating}
      />

      <EditSubscriptionDialog
        subscription={selectedSubscriptionForEdit}
        isOpen={isEditDialogOpen}
        onOpenChange={closeEditDialog}
        onSubmit={updateSubscription}
        isUpdating={isUpdating}
      />

      <DeleteSubscriptionDialog
        subscription={selectedSubscriptionForDelete}
        isOpen={isDeleteDialogOpen}
        onOpenChange={closeDeleteDialog}
        onConfirm={deleteSubscription}
        isDeleting={isDeleting}
      />
    </Listing>
  );
}
