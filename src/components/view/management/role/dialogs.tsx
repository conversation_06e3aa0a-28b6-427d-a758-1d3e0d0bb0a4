"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "@/components/common/ui/dialog";
import { <PERSON><PERSON> } from "@/components/common/ui/button";

import { AlertTriangle, Shield } from "lucide-react";
import { CreateRoleForm } from "./forms/create";
import { EditRoleForm } from "./forms/edit";
import { DeleteRoleForm } from "./forms/delete";
import type {
  Role,
  CreateRoleSchema,
  UpdateRoleSchema,
} from "@/lib/api/validators/schemas/role";
import { z } from "zod";

// Create Role Dialog Props
interface CreateRoleDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: z.infer<typeof CreateRoleSchema>) => Promise<void>;
  isCreating: boolean;
}

// Edit Role Dialog Props
interface EditRoleDialogProps {
  role: Role | null;
  isOpen: boolean;
  onSubmit: (
    id: string,
    data: Partial<z.infer<typeof UpdateRoleSchema>>
  ) => Promise<void>;
  onOpenChange: (open: boolean) => void;
  isUpdating: boolean;
}

// Delete Role Dialog Props
interface DeleteRoleDialogProps {
  role: Role | null;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (id: string) => Promise<void>;
  isDeleting: boolean;
}

// Create Role Dialog Component
export function CreateRoleDialog({
  isOpen,
  onOpenChange,
  onSubmit,
  isCreating,
}: CreateRoleDialogProps) {
  const handleSubmit = async (data: z.infer<typeof CreateRoleSchema>) => {
    try {
      await onSubmit(data);
      onOpenChange(false); // Close dialog on success
    } catch (error) {
      // Error handling is done in the parent component
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Create New Role
          </DialogTitle>
        </DialogHeader>

        <CreateRoleForm onSubmit={handleSubmit}>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isCreating}>
              {isCreating ? "Creating..." : "Create Role"}
            </Button>
          </DialogFooter>
        </CreateRoleForm>
      </DialogContent>
    </Dialog>
  );
}

// Edit Role Dialog Component
export function EditRoleDialog({
  role,
  isOpen,
  onOpenChange,
  onSubmit,
  isUpdating,
}: EditRoleDialogProps) {
  if (!role) return null;

  const handleSubmit = async (
    id: string,
    data: Partial<z.infer<typeof UpdateRoleSchema>>
  ) => {
    try {
      await onSubmit(id, data);
      onOpenChange(false); // Close dialog on success
    } catch (error) {
      // Error handling is done in the parent component
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Edit Role: {role.name}
          </DialogTitle>
        </DialogHeader>

        <EditRoleForm role={role} onSubmit={handleSubmit}>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isUpdating}>
              {isUpdating ? "Updating..." : "Update Role"}
            </Button>
          </DialogFooter>
        </EditRoleForm>
      </DialogContent>
    </Dialog>
  );
}

// Delete Role Dialog Component
export function DeleteRoleDialog({
  role,
  isOpen,
  onOpenChange,
  onConfirm,
  isDeleting,
}: DeleteRoleDialogProps) {
  if (!role) return null;

  const handleConfirm = async () => {
    try {
      await onConfirm(role.id);
      onOpenChange(false); // Close dialog on success
    } catch (error) {
      // Error handling is done in the parent component
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
            Delete Role: {role.name}
          </DialogTitle>
        </DialogHeader>

        <DeleteRoleForm role={role} onSuccess={() => onOpenChange(false)}>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete Role"}
            </Button>
          </DialogFooter>
        </DeleteRoleForm>
      </DialogContent>
    </Dialog>
  );
}
