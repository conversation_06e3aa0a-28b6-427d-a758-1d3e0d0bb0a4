"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/common/ui/card";
import { Checkbox } from "@/components/common/ui/checkbox";
import { Label } from "@/components/common/ui/label";
import { Badge } from "@/components/common/ui/badge";
import { Button } from "@/components/common/ui/button";
import { 
  Shield, 
  User, 
  FileText, 
  CreditCard, 
  Settings,
  CheckSquare,
  Square
} from "lucide-react";
import type { EntityPermissions, PermissionAction } from "@/lib/api/validators/schemas/role";
import defaultPermissions from "../default-permissions.json";

// Entity icons mapping
const entityIcons: Record<string, React.ComponentType<any>> = {
  user: User,
  subscription: CreditCard,
  request: FileText,
  role: Shield,
};

// Permission action labels
const actionLabels: Record<PermissionAction, string> = {
  create: "Create",
  read: "Read",
  update: "Update",
  delete: "Delete",
};

// Permission action colors
const actionColors: Record<PermissionAction, string> = {
  create: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  read: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  update: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  delete: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
};

interface PermissionsSelectorProps {
  permissions: EntityPermissions;
  onChange: (permissions: EntityPermissions) => void;
  disabled?: boolean;
}

export function PermissionsSelector({
  permissions,
  onChange,
  disabled = false,
}: PermissionsSelectorProps) {
  const [localPermissions, setLocalPermissions] = useState<EntityPermissions>(permissions);

  // Update local state when permissions prop changes
  useEffect(() => {
    setLocalPermissions(permissions);
  }, [permissions]);

  // Handle individual permission toggle
  const handlePermissionToggle = (entity: string, action: PermissionAction) => {
    const updatedPermissions = { ...localPermissions };
    
    if (!updatedPermissions[entity]) {
      updatedPermissions[entity] = [];
    }

    const entityPermissions = [...updatedPermissions[entity]];
    const actionIndex = entityPermissions.indexOf(action);

    if (actionIndex > -1) {
      // Remove permission
      entityPermissions.splice(actionIndex, 1);
    } else {
      // Add permission
      entityPermissions.push(action);
    }

    updatedPermissions[entity] = entityPermissions;
    setLocalPermissions(updatedPermissions);
    onChange(updatedPermissions);
  };

  // Handle entity select all/none
  const handleEntityToggle = (entity: string) => {
    const updatedPermissions = { ...localPermissions };
    const availableActions = defaultPermissions[entity as keyof typeof defaultPermissions] || [];
    const currentActions = updatedPermissions[entity] || [];
    
    if (currentActions.length === availableActions.length) {
      // All selected, remove all
      updatedPermissions[entity] = [];
    } else {
      // Not all selected, select all
      updatedPermissions[entity] = [...availableActions];
    }

    setLocalPermissions(updatedPermissions);
    onChange(updatedPermissions);
  };

  // Check if permission is selected
  const isPermissionSelected = (entity: string, action: PermissionAction): boolean => {
    return localPermissions[entity]?.includes(action) || false;
  };

  // Check if all entity permissions are selected
  const isEntityFullySelected = (entity: string): boolean => {
    const availableActions = defaultPermissions[entity as keyof typeof defaultPermissions] || [];
    const currentActions = localPermissions[entity] || [];
    return availableActions.length > 0 && currentActions.length === availableActions.length;
  };

  // Check if some entity permissions are selected
  const isEntityPartiallySelected = (entity: string): boolean => {
    const currentActions = localPermissions[entity] || [];
    return currentActions.length > 0 && !isEntityFullySelected(entity);
  };

  // Reset to default permissions
  const handleResetToDefault = () => {
    setLocalPermissions(defaultPermissions);
    onChange(defaultPermissions);
  };

  // Clear all permissions
  const handleClearAll = () => {
    const clearedPermissions: EntityPermissions = {};
    Object.keys(defaultPermissions).forEach(entity => {
      clearedPermissions[entity] = [];
    });
    setLocalPermissions(clearedPermissions);
    onChange(clearedPermissions);
  };

  return (
    <div className="space-y-4">
      {/* Action Buttons */}
      <div className="flex gap-2 flex-wrap">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={handleResetToDefault}
          disabled={disabled}
        >
          <Settings className="h-4 w-4 mr-2" />
          Reset to Default
        </Button>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={handleClearAll}
          disabled={disabled}
        >
          <Square className="h-4 w-4 mr-2" />
          Clear All
        </Button>
      </div>

      {/* Permissions Grid */}
      <div className="grid gap-4 md:grid-cols-2">
        {Object.entries(defaultPermissions).map(([entity, availableActions]) => {
          const IconComponent = entityIcons[entity] || Settings;
          const isFullySelected = isEntityFullySelected(entity);
          const isPartiallySelected = isEntityPartiallySelected(entity);

          return (
            <Card key={entity} className="relative">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center justify-between text-base">
                  <div className="flex items-center gap-2">
                    <IconComponent className="h-4 w-4" />
                    <span className="capitalize">{entity}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      {localPermissions[entity]?.length || 0}/{availableActions.length}
                    </Badge>
                    <Checkbox
                      checked={isFullySelected}
                      ref={(el) => {
                        if (el) {
                          el.indeterminate = isPartiallySelected;
                        }
                      }}
                      onCheckedChange={() => handleEntityToggle(entity)}
                      disabled={disabled}
                      aria-label={`Toggle all ${entity} permissions`}
                    />
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-2 gap-2">
                  {availableActions.map((action) => {
                    const isSelected = isPermissionSelected(entity, action);
                    
                    return (
                      <div key={action} className="flex items-center space-x-2">
                        <Checkbox
                          id={`${entity}-${action}`}
                          checked={isSelected}
                          onCheckedChange={() => handlePermissionToggle(entity, action)}
                          disabled={disabled}
                        />
                        <Label
                          htmlFor={`${entity}-${action}`}
                          className="text-sm font-normal cursor-pointer flex items-center gap-1"
                        >
                          <Badge 
                            variant="secondary" 
                            className={`text-xs px-1.5 py-0.5 ${actionColors[action]}`}
                          >
                            {actionLabels[action]}
                          </Badge>
                        </Label>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Summary */}
      <div className="text-sm text-muted-foreground">
        <p>
          Total permissions selected: {" "}
          <span className="font-medium">
            {Object.values(localPermissions).reduce((total, actions) => total + actions.length, 0)}
          </span>
          {" "} of {" "}
          <span className="font-medium">
            {Object.values(defaultPermissions).reduce((total, actions) => total + actions.length, 0)}
          </span>
        </p>
      </div>
    </div>
  );
}
