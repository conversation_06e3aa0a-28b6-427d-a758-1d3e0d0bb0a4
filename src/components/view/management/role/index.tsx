"use client";

import React, { useState } from "react";
import { Listing } from "@/layouts/dashboard/details/basic";
import { <PERSON><PERSON> } from "@/components/common/ui/button";
import { Badge } from "@/components/common/ui/badge";
import {
  Shield,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Key,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/common/ui/dropdown-menu";
import type { Role } from "@/lib/api/validators/schemas/role";
import { useRoles } from "@/hooks/useRoles";
import { CreateRoleDialog, EditRoleDialog, DeleteRoleDialog } from "./dialogs";

export function RoleManagement() {
  // Dialog state management
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);

  // Initialize useRoles hook
  const {
    filteredRoles,
    isLoading,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    createRole,
    updateRole,
    deleteRole,
    refreshRoles,
    isCreating,
    isUpdating,
    isDeleting,
  } = useRoles({
    page: 1,
    limit: 50,
    autoFetch: true,
  });

  // Handle create role
  const handleCreateRole = async (roleData: any) => {
    await createRole(roleData);
  };

  // Handle update role
  const handleUpdateRole = async (id: string, roleData: any) => {
    await updateRole(id, roleData);
  };

  // Handle delete role
  const handleDeleteRole = async (id: string) => {
    await deleteRole(id);
  };

  // Dialog handlers
  const handleOpenCreateDialog = () => {
    setCreateDialogOpen(true);
  };

  const handleOpenEditDialog = (role: Role) => {
    setSelectedRole(role);
    setEditDialogOpen(true);
  };

  const handleOpenDeleteDialog = (role: Role) => {
    setSelectedRole(role);
    setDeleteDialogOpen(true);
  };

  // Helper function to count permissions
  const getPermissionCount = (permissions: any) => {
    if (!permissions || typeof permissions !== "object") return 0;
    return Object.values(permissions).reduce((total: number, actions: any) => {
      return total + (Array.isArray(actions) ? actions.length : 0);
    }, 0);
  };

  // Helper function to get permission entities
  const getPermissionEntities = (permissions: any) => {
    if (!permissions || typeof permissions !== "object") return [];
    return Object.keys(permissions);
  };

  // Role table columns
  const roleColumns = [
    {
      key: "role",
      label: "Role",
      render: (role: Role) => (
        <div className="flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
            <Shield className="h-5 w-5 text-primary" />
          </div>
          <div>
            <p className="font-medium">{role.name}</p>
            <p className="text-sm text-muted-foreground">
              {role.description || "No description"}
            </p>
          </div>
        </div>
      ),
    },
    {
      key: "permissions",
      label: "Permissions",
      render: (role: Role) => (
        <div className="flex items-center gap-2">
          <Key className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">
            {getPermissionCount(role.permissions)} permissions across{" "}
            {getPermissionEntities(role.permissions).length} entities
          </span>
        </div>
      ),
    },
    {
      key: "entities",
      label: "Entities",
      render: (role: Role) => (
        <div className="flex flex-wrap gap-1">
          {getPermissionEntities(role.permissions)
            .slice(0, 3)
            .map((entity) => (
              <Badge key={entity} variant="outline" className="text-xs">
                {entity}
              </Badge>
            ))}
          {getPermissionEntities(role.permissions).length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{getPermissionEntities(role.permissions).length - 3} more
            </Badge>
          )}
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (role: Role) => (
        <Badge
          variant={role.status === "active" ? "default" : "secondary"}
          className="capitalize"
        >
          {role.status}
        </Badge>
      ),
    },
    {
      key: "actions",
      label: "Actions",
      render: (role: Role) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>
              <Eye className="mr-2 h-4 w-4" />
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleOpenEditDialog(role)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Role
            </DropdownMenuItem>
            <DropdownMenuItem
              className="text-destructive"
              onClick={() => handleOpenDeleteDialog(role)}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Role
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Role Management Section */}
      <Listing className="space-y-1">
        <Listing.Header
          title={{ text: "Role Directory", size: "md" }}
          actions={
            <Button onClick={handleOpenCreateDialog}>
              <Plus size={16} className="mr-2" />
              Add Role
            </Button>
          }
        />

        {/* Filters */}
        <Listing.Filters
          searchTerm={searchQuery}
          onSearchChange={setSearchQuery}
          onRefresh={refreshRoles}
          loading={isLoading}
          customActions={
            <div className="flex items-center gap-2">
              <Button
                variant={!filters.status ? "default" : "outline"}
                size="sm"
                onClick={() => setFilters({ ...filters, status: undefined })}
              >
                All
              </Button>
              <Button
                variant={filters.status === "active" ? "default" : "outline"}
                size="sm"
                onClick={() => setFilters({ ...filters, status: "active" })}
              >
                Active
              </Button>
              <Button
                variant={filters.status === "inactive" ? "default" : "outline"}
                size="sm"
                onClick={() => setFilters({ ...filters, status: "inactive" })}
              >
                Inactive
              </Button>
            </div>
          }
        />

        {/* Role Table */}
        <Listing.Table
          data={filteredRoles}
          columns={roleColumns}
          loading={isLoading}
          emptyState={
            <div className="text-center py-8">
              <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {searchQuery || filters.status
                  ? "No roles match your filters"
                  : "No roles found"}
              </p>
              <Button className="mt-4" onClick={handleOpenCreateDialog}>
                <Plus size={16} className="mr-2" />
                Add Your First Role
              </Button>
            </div>
          }
        />
      </Listing>

      {/* Dialogs */}
      <CreateRoleDialog
        isOpen={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSubmit={handleCreateRole}
        isCreating={isCreating}
      />

      <EditRoleDialog
        role={selectedRole}
        isOpen={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        onSubmit={handleUpdateRole}
        isUpdating={isUpdating}
      />

      <DeleteRoleDialog
        role={selectedRole}
        isOpen={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDeleteRole}
        isDeleting={isDeleting}
      />
    </div>
  );
}
