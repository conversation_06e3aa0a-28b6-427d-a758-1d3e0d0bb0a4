"use client";

import React, { useState } from "react";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import { Textarea } from "@/components/common/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";
import { toast } from "sonner";
import type {
  CreateRoleSchema,
  EntityPermissions,
} from "@/lib/api/validators/schemas/role";
import { z } from "zod";
import { PermissionsSelector } from "../components/PermissionsSelector";

// Create Role Form Props
interface CreateRoleFormProps {
  onSubmit: (data: z.infer<typeof CreateRoleSchema>) => Promise<void>;
  children?: React.ReactNode;
}

// Create Role Form Component
export function CreateRoleForm({ onSubmit, children }: CreateRoleFormProps) {
  const [formData, setFormData] = useState<z.infer<typeof CreateRoleSchema>>({
    name: "",
    description: "",
    status: "created",
    permissions: {},
  });

  const handleInputChange = (
    field: keyof z.infer<typeof CreateRoleSchema>,
    value: any
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handlePermissionsChange = (permissions: EntityPermissions) => {
    setFormData((prev) => ({
      ...prev,
      permissions,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Basic validation
      if (!formData.name?.trim()) {
        toast.error("Role name is required");
        return;
      }

      await onSubmit(formData);
      toast.success("Role created successfully");
    } catch (error) {
      console.error("Failed to create role:", error);
      toast.error("Failed to create role");
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="create-name">Role Name *</Label>
          <Input
            id="create-name"
            value={formData.name}
            onChange={(e) => handleInputChange("name", e.target.value)}
            placeholder="Enter role name"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="create-description">Description</Label>
          <Textarea
            id="create-description"
            value={formData.description || ""}
            onChange={(e) => handleInputChange("description", e.target.value)}
            placeholder="Enter role description"
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="create-status">Status</Label>
          <Select
            value={formData.status}
            onValueChange={(value) => handleInputChange("status", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="created">Created</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Permissions Section */}
      <div className="space-y-4">
        <div className="space-y-2">
          <Label>Permissions</Label>
          <PermissionsSelector
            permissions={formData.permissions}
            onChange={handlePermissionsChange}
          />
        </div>
      </div>

      {/* Form Actions */}
      {children}
    </form>
  );
}
