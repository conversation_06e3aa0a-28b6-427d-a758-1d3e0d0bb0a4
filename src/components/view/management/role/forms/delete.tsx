"use client";

import React from "react";
import { AlertTriangle } from "lucide-react";
import type { Role } from "@/lib/api/validators/schemas/role";

// Delete Role Form Props
interface DeleteRoleFormProps {
  role: Role;
  onSuccess?: () => void;
  children?: React.ReactNode;
}

// Delete Role Form Component
export function DeleteRoleForm({
  role,
  onSuccess,
  children,
}: DeleteRoleFormProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-start gap-3">
        <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">
            Are you sure you want to delete this role? This action cannot be
            undone.
          </p>
          <div className="bg-muted p-3 rounded-md">
            <div className="space-y-1">
              <p className="text-sm font-medium">{role.name}</p>
              {role.description && (
                <p className="text-xs text-muted-foreground">
                  {role.description}
                </p>
              )}
              <p className="text-xs text-muted-foreground">
                Status: <span className="capitalize">{role.status}</span>
              </p>
              <p className="text-xs text-muted-foreground">
                Permissions:{" "}
                {Object.values(role.permissions || {}).reduce(
                  (total, actions) => total + actions.length,
                  0
                )}{" "}
                total
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Form Actions */}
      {children}
    </div>
  );
}
