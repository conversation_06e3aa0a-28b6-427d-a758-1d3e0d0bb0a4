"use client";

import React, { useState, useEffect } from "react";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import { Textarea } from "@/components/common/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";
import { toast } from "sonner";
import type {
  Role,
  UpdateRoleSchema,
  EntityPermissions,
} from "@/lib/api/validators/schemas/role";
import { z } from "zod";
import { PermissionsSelector } from "../components/PermissionsSelector";

// Edit Role Form Props
interface EditRoleFormProps {
  onSubmit: (
    id: string,
    data: Partial<z.infer<typeof UpdateRoleSchema>>
  ) => Promise<void>;
  role: Role;
  children?: React.ReactNode;
}

// Edit Role Form Component
export function EditRoleForm({ onSubmit, role, children }: EditRoleFormProps) {
  const [formData, setFormData] = useState<
    Partial<z.infer<typeof UpdateRoleSchema>>
  >({});

  // Initialize form data when role changes
  useEffect(() => {
    if (role) {
      setFormData({
        id: role.id,
        name: role.name,
        description: role.description || "",
        status: role.status,
        permissions: role.permissions || {},
      });
    }
  }, [role]);

  const handleInputChange = (
    field: keyof z.infer<typeof UpdateRoleSchema>,
    value: any
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handlePermissionsChange = (permissions: EntityPermissions) => {
    setFormData((prev) => ({
      ...prev,
      permissions,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Basic validation
      if (!formData.name?.trim()) {
        toast.error("Role name is required");
        return;
      }

      if (!formData.status) {
        delete formData.status;
      }

      await onSubmit(role.id, formData);
      toast.success("Role updated successfully");
    } catch (error) {
      console.error("Failed to update role:", error);
      toast.error("Failed to update role");
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="edit-name">Role Name *</Label>
          <Input
            id="edit-name"
            value={formData.name || ""}
            onChange={(e) => handleInputChange("name", e.target.value)}
            placeholder="Enter role name"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="edit-description">Description</Label>
          <Textarea
            id="edit-description"
            value={formData.description || ""}
            onChange={(e) => handleInputChange("description", e.target.value)}
            placeholder="Enter role description"
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="edit-status">Status</Label>
          <Select
            value={formData.status}
            onValueChange={(value) => handleInputChange("status", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="created">Created</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Permissions Section */}
      <div className="space-y-4">
        <div className="space-y-2">
          <Label>Permissions</Label>
          <PermissionsSelector
            permissions={formData.permissions || {}}
            onChange={handlePermissionsChange}
          />
        </div>
      </div>

      {/* Form Actions */}
      {children}
    </form>
  );
}
