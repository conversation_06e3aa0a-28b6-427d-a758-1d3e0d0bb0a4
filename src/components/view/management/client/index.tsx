"use client";

import React, { useState } from "react";
import { Listing } from "@/layouts/dashboard/details/basic";
import { <PERSON><PERSON> } from "@/components/common/ui/button";
import { Badge } from "@/components/common/ui/badge";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/common/ui/avatar";
import {
  Building2,
  Mail,
  Phone,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/common/ui/dropdown-menu";
import { useClients } from "@/hooks/useUsers";
import {
  CreateClientDialog,
  EditClientDialog,
  DeleteClientDialog,
} from "./dialogs";
import type { User } from "@/lib/api/validators/schemas/user";

export function ClientManagement() {
  // Dialog state management
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogO<PERSON>, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedClient, setSelectedClient] = useState<User | null>(null);

  // Initialize useClients hook for client-specific data
  const {
    filteredUsers: clientUsers,
    isLoading,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    createClient,
    updateClient,
    deleteUser,
    refreshUsers,
    isCreating,
    isUpdating,
    isDeleting,
  } = useClients({
    page: 1,
    limit: 10,
    autoFetch: true,
    initialParams: {
      includeCompany: true, // Include company data for clients
    },
  });

  // Handle refresh action
  const handleRefresh = () => {
    refreshUsers();
  };

  // Handle create client
  const handleCreateClient = async (clientData: any) => {
    await createClient(clientData);
  };

  // Handle update client
  const handleUpdateClient = async (id: string, clientData: any) => {
    await updateClient(id, clientData);
  };

  // Handle delete client
  const handleDeleteClient = async (id: string) => {
    await deleteUser(id);
  };

  // Dialog handlers
  const handleOpenCreateDialog = () => {
    setCreateDialogOpen(true);
  };

  const handleOpenEditDialog = (client: User) => {
    setSelectedClient(client);
    setEditDialogOpen(true);
  };

  const handleOpenDeleteDialog = (client: User) => {
    setSelectedClient(client);
    setDeleteDialogOpen(true);
  };

  // Client table columns
  const clientColumns = [
    {
      key: "user",
      label: "Client",
      render: (client: any) => (
        <div className="flex items-center gap-3">
          <Avatar className="h-10 w-10">
            <AvatarImage
              src={client.image}
              alt={`${client.firstName} ${client.lastName}`}
            />
            <AvatarFallback>
              {client.firstName?.[0]}
              {client.lastName?.[0]}
            </AvatarFallback>
          </Avatar>
          <div>
            <p className="font-medium">
              {client.firstName} {client.lastName}
            </p>
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Mail className="h-3 w-3" />
              {client.email}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: "company",
      label: "Company",
      render: (client: any) => (
        <div className="flex items-center gap-2">
          <Building2 className="h-4 w-4 text-muted-foreground" />
          <span>{client.company?.name || "No Company"}</span>
        </div>
      ),
    },
    {
      key: "contact",
      label: "Contact",
      render: (client: any) => (
        <div className="flex items-center gap-1 text-sm">
          <Phone className="h-3 w-3 text-muted-foreground" />
          {client.phone || "No phone"}
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (client: any) => (
        <Badge
          variant={client.status === "active" ? "default" : "secondary"}
          className="capitalize"
        >
          {client.status}
        </Badge>
      ),
    },
    {
      key: "actions",
      label: "Actions",
      render: (client: any) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>
              <Eye className="mr-2 h-4 w-4" />
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleOpenEditDialog(client)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Client
            </DropdownMenuItem>
            <DropdownMenuItem
              className="text-destructive"
              onClick={() => handleOpenDeleteDialog(client)}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Client
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Client Statistics */}
      <Listing.Statistics columns="grid-cols-3">
        <Listing.StatCard
          name="Total Clients"
          value={clientUsers.length}
          valueType="number"
          caption="Active client accounts"
          color="blue"
        />
        <Listing.StatCard
          name="Active Clients"
          value={clientUsers.filter((c) => c.status === "active").length}
          valueType="number"
          caption="Currently active"
          color="green"
        />
        <Listing.StatCard
          name="With Companies"
          value={clientUsers.filter((c) => c.companyId).length}
          valueType="number"
          caption="Clients with companies"
          color="primary"
        />
      </Listing.Statistics>

      {/* Client Management Section */}
      <Listing className="space-y-1">
        <Listing.Header
          title={{ text: "Client Directory", size: "md" }}
          actions={
            <Button onClick={handleOpenCreateDialog}>
              <Plus size={16} className="mr-2" />
              Add Client
            </Button>
          }
        />

        {/* Filters */}
        <Listing.Filters
          searchTerm={searchQuery}
          onSearchChange={setSearchQuery}
          onRefresh={handleRefresh}
          loading={isLoading}
          customActions={
            <div className="flex items-center gap-2">
              <Button
                variant={!filters.status ? "default" : "outline"}
                size="sm"
                onClick={() => setFilters({ ...filters, status: undefined })}
              >
                All
              </Button>
              <Button
                variant={filters.status === "active" ? "default" : "outline"}
                size="sm"
                onClick={() => setFilters({ ...filters, status: "active" })}
              >
                Active
              </Button>
              <Button
                variant={filters.status === "inactive" ? "default" : "outline"}
                size="sm"
                onClick={() => setFilters({ ...filters, status: "inactive" })}
              >
                Inactive
              </Button>
            </div>
          }
        />

        {/* Client Table */}
        <Listing.Table
          data={clientUsers}
          columns={clientColumns}
          loading={isLoading}
          emptyState={
            <div className="text-center py-8">
              <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {searchQuery || filters.status
                  ? "No clients match your filters"
                  : "No clients found"}
              </p>
              <Button className="mt-4" onClick={handleOpenCreateDialog}>
                <Plus size={16} className="mr-2" />
                Add Your First Client
              </Button>
            </div>
          }
        />
      </Listing>

      {/* Dialogs */}
      <CreateClientDialog
        isOpen={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSubmit={handleCreateClient}
        isCreating={isCreating}
      />

      <EditClientDialog
        client={selectedClient}
        isOpen={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        onSubmit={handleUpdateClient}
        isUpdating={isUpdating}
      />

      <DeleteClientDialog
        client={selectedClient}
        isOpen={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDeleteClient}
        isDeleting={isDeleting}
      />
    </div>
  );
}
