"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "@/components/common/ui/dialog";
import { <PERSON><PERSON> } from "@/components/common/ui/button";

import { AlertTriangle, Building2 } from "lucide-react";
import { CreateClientForm } from "./forms/create";
import { EditClientForm } from "./forms/edit";
import { DeleteClientForm } from "./forms/delete";
import type {
  User,
  CreateClientSchema,
  UpdateClientSchema,
} from "@/lib/api/validators/schemas/user";
import { z } from "zod";

// Create Client Dialog Props
interface CreateClientDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: z.infer<typeof CreateClientSchema>) => Promise<void>;
  isCreating: boolean;
}

// Edit Client Dialog Props
interface EditClientDialogProps {
  client: User | null;
  isOpen: boolean;
  onSubmit: (
    id: string,
    data: Partial<z.infer<typeof UpdateClientSchema>>
  ) => Promise<void>;
  onOpenChange: (open: boolean) => void;
  isUpdating: boolean;
}

// Delete Client Dialog Props
interface DeleteClientDialogProps {
  client: User | null;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (id: string) => Promise<void>;
  isDeleting: boolean;
}

// Create Client Dialog Component
export function CreateClientDialog({
  isOpen,
  onOpenChange,
  onSubmit,
  isCreating,
}: CreateClientDialogProps) {
  const handleSubmit = async (data: z.infer<typeof CreateClientSchema>) => {
    try {
      await onSubmit(data);
      onOpenChange(false); // Close dialog on success
    } catch (error) {
      // Error handling is done in the parent component
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Create New Client
          </DialogTitle>
        </DialogHeader>

        <CreateClientForm onSubmit={handleSubmit}>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isCreating}>
              {isCreating ? "Creating..." : "Create Client"}
            </Button>
          </DialogFooter>
        </CreateClientForm>
      </DialogContent>
    </Dialog>
  );
}

// Edit Client Dialog Component
export function EditClientDialog({
  client,
  isOpen,
  onOpenChange,
  onSubmit,
  isUpdating,
}: EditClientDialogProps) {
  if (!client) return null;

  const handleSubmit = async (
    id: string,
    data: Partial<z.infer<typeof UpdateClientSchema>>
  ) => {
    try {
      await onSubmit(id, data);
      onOpenChange(false); // Close dialog on success
    } catch (error) {
      // Error handling is done in the parent component
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Edit Client: {client.firstName} {client.lastName}
          </DialogTitle>
        </DialogHeader>

        <EditClientForm client={client} onSubmit={handleSubmit}>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isUpdating}>
              {isUpdating ? "Updating..." : "Update Client"}
            </Button>
          </DialogFooter>
        </EditClientForm>
      </DialogContent>
    </Dialog>
  );
}

// Delete Client Dialog Component
export function DeleteClientDialog({
  client,
  isOpen,
  onOpenChange,
  onConfirm,
  isDeleting,
}: DeleteClientDialogProps) {
  if (!client) return null;

  const handleConfirm = async () => {
    try {
      await onConfirm(client.id);
      onOpenChange(false);
    } catch (error) {
      // Error handling is done in the parent component
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
            Delete Client: {client.firstName} {client.lastName}
          </DialogTitle>
        </DialogHeader>

        <DeleteClientForm client={client} onSuccess={() => onOpenChange(false)}>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete Client"}
            </Button>
          </DialogFooter>
        </DeleteClientForm>
      </DialogContent>
    </Dialog>
  );
}
