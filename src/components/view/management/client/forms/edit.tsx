"use client";

import React, { useState, useEffect } from "react";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";
import { toast } from "sonner";
import { useSubscriptions } from "@/hooks/useSubscriptions";
import type {
  User,
  UpdateClientSchema,
} from "@/lib/api/validators/schemas/user";
import { z } from "zod";

// Edit Client Form Props
interface EditClientFormProps {
  onSubmit: (
    id: string,
    data: Partial<z.infer<typeof UpdateClientSchema>>
  ) => Promise<void>;
  client: User;
  children?: React.ReactNode;
}

// Edit Client Form Component
export function EditClientForm({
  onSubmit,
  client,
  children,
}: EditClientFormProps) {
  const [formData, setFormData] = useState<
    Partial<z.infer<typeof UpdateClientSchema>>
  >({});

  // Fetch subscriptions for the dropdown
  const { subscriptions, isLoading: subscriptionsLoading } = useSubscriptions();

  // Initialize form data when client changes - only editable fields
  useEffect(() => {
    if (client) {
      setFormData({
        id: client.id,
        status: client.status,
        subscriptionId: (client as any).subscriptionId || "",
      });
    }
  }, [client]);

  const handleInputChange = (
    field: keyof z.infer<typeof UpdateClientSchema>,
    value: any
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Only validate required fields for editing (status is required)
    if (!formData.status) {
      toast.error("Status is required");
      return;
    }

    try {
      await onSubmit(client.id, formData);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Client Information (Read-Only) */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">
          Client Information
        </h3>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>First Name</Label>
            <Input
              value={client.firstName}
              disabled
              className="bg-gray-50 text-gray-600"
            />
          </div>

          <div className="space-y-2">
            <Label>Last Name</Label>
            <Input
              value={client.lastName}
              disabled
              className="bg-gray-50 text-gray-600"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label>Email</Label>
          <Input
            value={client.email}
            disabled
            className="bg-gray-50 text-gray-600"
          />
        </div>

        <div className="space-y-2">
          <Label>Phone</Label>
          <Input
            value={client.phone || "Not provided"}
            disabled
            className="bg-gray-50 text-gray-600"
          />
        </div>

        {/* Company Information (Read-Only) */}
        {(client as any).company && (
          <div className="space-y-2">
            <Label>Company</Label>
            <div className="p-3 bg-gray-50 border rounded-md">
              <div className="font-medium text-gray-900">
                {(client as any).company.name}
              </div>
              {(client as any).company.category && (
                <div className="text-sm text-gray-600">
                  {(client as any).company.category}
                </div>
              )}
              {(client as any).company.email && (
                <div className="text-sm text-gray-600">
                  {(client as any).company.email}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Editable Fields */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Editable Settings</h3>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="edit-status">Status *</Label>
            <Select
              value={formData.status}
              onValueChange={(value) => handleInputChange("status", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created">Created</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="edit-subscription">Subscription</Label>
            <Select
              value={formData.subscriptionId || ""}
              onValueChange={(value) =>
                handleInputChange("subscriptionId", value)
              }
              disabled={subscriptionsLoading}
            >
              <SelectTrigger>
                <SelectValue
                  placeholder={
                    subscriptionsLoading
                      ? "Loading subscriptions..."
                      : "Select a subscription"
                  }
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">No subscription</SelectItem>
                {subscriptions.map((subscription) => (
                  <SelectItem key={subscription.id} value={subscription.id}>
                    <div className="flex flex-col">
                      <span>{subscription.name}</span>
                      {subscription.description && (
                        <span className="text-sm text-muted-foreground">
                          {subscription.description}
                        </span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {children}
    </form>
  );
}
