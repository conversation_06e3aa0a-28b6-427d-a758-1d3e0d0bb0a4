"use client";

import React, { useState, useEffect } from "react";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";
import { toast } from "sonner";
import { useSubscriptions } from "@/hooks/useSubscriptions";
import type {
  User,
  UpdateClientSchema,
} from "@/lib/api/validators/schemas/user";
import { z } from "zod";

// Edit Client Form Props
interface EditClientFormProps {
  onSubmit: (
    id: string,
    data: Partial<z.infer<typeof UpdateClientSchema>>
  ) => Promise<void>;
  client: User;
  children?: React.ReactNode;
}

// Edit Client Form Component
export function EditClientForm({
  onSubmit,
  client,
  children,
}: EditClientFormProps) {
  const [formData, setFormData] = useState<
    Partial<z.infer<typeof UpdateClientSchema>>
  >({});

  // Fetch subscriptions for the dropdown
  const { subscriptions, isLoading: subscriptionsLoading } = useSubscriptions();

  // Initialize form data when client changes - only editable fields
  useEffect(() => {
    if (client) {
      setFormData({
        id: client.id,
        status: client.status,
        subscriptionId: (client as any).subscriptionId || "",
      });
    }
  }, [client]);

  const handleInputChange = (
    field: keyof z.infer<typeof UpdateClientSchema>,
    value: any
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Only validate required fields for editing (status is required)
    if (!formData.status) {
      toast.error("Status is required");
      return;
    }

    try {
      await onSubmit(client.id, formData);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information (Read-Only) */}
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="firstName">First Name</Label>
            <Input
              id="firstName"
              value={client.firstName}
              disabled
              className="bg-gray-50 text-gray-600"
              placeholder="First name"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name</Label>
            <Input
              id="lastName"
              value={client.lastName}
              disabled
              className="bg-gray-50 text-gray-600"
              placeholder="Last name"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            value={client.email}
            disabled
            className="bg-gray-50 text-gray-600"
            placeholder="Email address"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="phone">Phone</Label>
          <Input
            id="phone"
            value={client.phone || ""}
            disabled
            className="bg-gray-50 text-gray-600"
            placeholder="Phone number"
          />
        </div>

        {/* Company Information (Read-Only) */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">
            Company Information
          </h3>

          {(client as any).company ? (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="companyName">Company Name</Label>
                  <Input
                    id="companyName"
                    value={(client as any).company.name || ""}
                    disabled
                    className="bg-gray-50 text-gray-600"
                    placeholder="Company name"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="companyTin">Company TIN</Label>
                  <Input
                    id="companyTin"
                    value={(client as any).company.tin || ""}
                    disabled
                    className="bg-gray-50 text-gray-600"
                    placeholder="Company TIN"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="companyCategory">Company Category</Label>
                  <Input
                    id="companyCategory"
                    value={(client as any).company.category || ""}
                    disabled
                    className="bg-gray-50 text-gray-600"
                    placeholder="Company category"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="companyEmail">Company Email</Label>
                  <Input
                    id="companyEmail"
                    type="email"
                    value={(client as any).company.email || ""}
                    disabled
                    className="bg-gray-50 text-gray-600"
                    placeholder="Company email"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="companyAddress">Company Address</Label>
                <Input
                  id="companyAddress"
                  value={(client as any).company.address || ""}
                  disabled
                  className="bg-gray-50 text-gray-600"
                  placeholder="Company address"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="companyPhone">Company Phone</Label>
                <Input
                  id="companyPhone"
                  value={(client as any).company.phone || ""}
                  disabled
                  className="bg-gray-50 text-gray-600"
                  placeholder="Company phone"
                />
              </div>
            </>
          ) : (
            <div className="p-4 bg-gray-50 border rounded-md text-center text-gray-500">
              No company information available
            </div>
          )}
        </div>

        {/* Client Settings (Editable) */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="subscription">Subscription</Label>
            <Select
              value={formData.subscriptionId || ""}
              onValueChange={(value) =>
                handleInputChange("subscriptionId", value)
              }
              disabled={subscriptionsLoading}
            >
              <SelectTrigger>
                <SelectValue
                  placeholder={
                    subscriptionsLoading
                      ? "Loading subscriptions..."
                      : "Select a subscription"
                  }
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">No subscription</SelectItem>
                {subscriptions.map((subscription) => (
                  <SelectItem key={subscription.id} value={subscription.id}>
                    <div className="flex flex-col">
                      <span>{subscription.name}</span>
                      {subscription.description && (
                        <span className="text-sm text-muted-foreground">
                          {subscription.description}
                        </span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={formData.status}
              onValueChange={(value) => handleInputChange("status", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created">Created</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {children}
    </form>
  );
}
