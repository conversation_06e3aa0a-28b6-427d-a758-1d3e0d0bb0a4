"use client";

import React, { useState } from "react";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";
import { toast } from "sonner";
import { useSubscriptions } from "@/hooks/useSubscriptions";
import type { CreateClientSchema } from "@/lib/api/validators/schemas/user";
import { z } from "zod";

// Create Client Form Props
interface CreateClientFormProps {
  children?: React.ReactNode;
  onSubmit: (data: ExtendedCreateClientData) => Promise<void>;
}

// Extended form data type to include company creation fields
type ExtendedCreateClientData = z.infer<typeof CreateClientSchema> & {
  // Company creation fields
  companyName: string;
  companyTin: string;
  companyCategory: string;
  companyEmail: string;
  companyAddress: string;
  companyPhone: string;
};

// Initial form data for create (removed password fields, added company fields)
const INITIAL_CREATE_FORM_DATA: ExtendedCreateClientData = {
  firstName: "",
  lastName: "",
  email: "",
  phone: "",
  status: "created",
  type: "client",
  companyId: "",
  subscriptionId: "",
  // Company creation fields
  companyName: "",
  companyTin: "",
  companyCategory: "",
  companyEmail: "",
  companyAddress: "",
  companyPhone: "",
};

// Create Client Form Component
export function CreateClientForm({
  children,
  onSubmit,
}: CreateClientFormProps) {
  const [formData, setFormData] = useState<ExtendedCreateClientData>(
    INITIAL_CREATE_FORM_DATA
  );

  // Fetch subscriptions for the dropdown
  const { subscriptions, isLoading: subscriptionsLoading } = useSubscriptions();

  const handleInputChange = (
    field: keyof ExtendedCreateClientData,
    value: any
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formData.firstName.trim()) {
      toast.error("First name is required");
      return;
    }

    if (!formData.lastName.trim()) {
      toast.error("Last name is required");
      return;
    }

    if (!formData.email.trim()) {
      toast.error("Email is required");
      return;
    }

    // Company validation
    if (!formData.companyName.trim()) {
      toast.error("Company name is required");
      return;
    }

    if (!formData.companyTin.trim()) {
      toast.error("Company TIN is required");
      return;
    }

    try {
      // Pass the complete form data (client + company fields)
      await onSubmit(formData);
      // Reset form after successful submission
      setFormData(INITIAL_CREATE_FORM_DATA);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="firstName">First Name *</Label>
            <Input
              id="firstName"
              value={formData.firstName}
              onChange={(e) => handleInputChange("firstName", e.target.value)}
              placeholder="Enter first name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name *</Label>
            <Input
              id="lastName"
              value={formData.lastName}
              onChange={(e) => handleInputChange("lastName", e.target.value)}
              placeholder="Enter last name"
              required
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email *</Label>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange("email", e.target.value)}
            placeholder="Enter email address"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="phone">Phone</Label>
          <Input
            id="phone"
            value={formData.phone || ""}
            onChange={(e) => handleInputChange("phone", e.target.value)}
            placeholder="Enter phone number"
          />
        </div>

        {/* Company Creation Fields */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">
            Company Information
          </h3>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="companyName">Company Name *</Label>
              <Input
                id="companyName"
                value={formData.companyName}
                onChange={(e) =>
                  handleInputChange("companyName", e.target.value)
                }
                placeholder="Enter company name"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="companyTin">Company TIN *</Label>
              <Input
                id="companyTin"
                value={formData.companyTin}
                onChange={(e) =>
                  handleInputChange("companyTin", e.target.value)
                }
                placeholder="Enter company TIN"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="companyCategory">Company Category</Label>
              <Input
                id="companyCategory"
                value={formData.companyCategory}
                onChange={(e) =>
                  handleInputChange("companyCategory", e.target.value)
                }
                placeholder="Enter company category"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="companyEmail">Company Email</Label>
              <Input
                id="companyEmail"
                type="email"
                value={formData.companyEmail}
                onChange={(e) =>
                  handleInputChange("companyEmail", e.target.value)
                }
                placeholder="Enter company email"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="companyAddress">Company Address</Label>
            <Input
              id="companyAddress"
              value={formData.companyAddress}
              onChange={(e) =>
                handleInputChange("companyAddress", e.target.value)
              }
              placeholder="Enter company address"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="companyPhone">Company Phone</Label>
            <Input
              id="companyPhone"
              value={formData.companyPhone}
              onChange={(e) =>
                handleInputChange("companyPhone", e.target.value)
              }
              placeholder="Enter company phone"
            />
          </div>
        </div>

        {/* Client Settings */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="subscription">Subscription</Label>
            <Select
              value={formData.subscriptionId}
              onValueChange={(value) =>
                handleInputChange("subscriptionId", value)
              }
              disabled={subscriptionsLoading}
            >
              <SelectTrigger>
                <SelectValue
                  placeholder={
                    subscriptionsLoading
                      ? "Loading subscriptions..."
                      : "Select a subscription"
                  }
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="...">No subscription</SelectItem>
                {subscriptions.map((subscription) => (
                  <SelectItem key={subscription.id} value={subscription.id}>
                    <div className="flex flex-col">
                      <span>{subscription.name}</span>
                      {subscription.description && (
                        <span className="text-sm text-muted-foreground">
                          {subscription.description}
                        </span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={formData.status}
              onValueChange={(value) => handleInputChange("status", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created">Created</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {children}
    </form>
  );
}
