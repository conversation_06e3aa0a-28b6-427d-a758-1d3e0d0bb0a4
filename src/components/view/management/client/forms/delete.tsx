"use client";

import React from "react";

import { AlertTriangle } from "lucide-react";

import type { User } from "@/lib/api/validators/schemas/user";

// Delete Client Form Props
interface DeleteClientFormProps {
  client: User;
  children?: React.ReactNode;
  onSuccess?: () => void;
}

// Delete Client Form Component
export function DeleteClientForm({
  client,
  children,
  onSuccess,
}: DeleteClientFormProps) {
  return (
    <div className="space-y-4">
      <div className="space-y-4">
        <p className="text-sm text-muted-foreground">
          Are you sure you want to delete the client "{client.firstName}{" "}
          {client.lastName}
          "? This action cannot be undone and will permanently remove the client
          and all associated data.
        </p>

        <div className="bg-amber-50 border border-amber-200 rounded-md p-3">
          <div className="flex items-center gap-2 text-amber-800">
            <AlertTriangle className="h-4 w-4" />
            <span className="text-sm font-medium">
              Warning: This will permanently delete the client account.
            </span>
          </div>
        </div>

        <div className="bg-muted p-3 rounded-md">
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Name:</span>
              <span className="text-foreground font-medium">
                {client.firstName} {client.lastName}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Email:</span>
              <span className="text-foreground font-medium">
                {client.email}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Type:</span>
              <span className="text-foreground font-medium capitalize">
                {client.type}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Status:</span>
              <span className="text-foreground font-medium capitalize">
                {client.status}
              </span>
            </div>
            {client.companyId && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Company ID:</span>
                <span className="text-foreground font-medium">
                  {client.companyId}
                </span>
              </div>
            )}
            {client.customerId && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Customer ID:</span>
                <span className="text-foreground font-medium">
                  {client.customerId}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {children}
    </div>
  );
}
