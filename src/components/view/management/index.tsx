"use client";

import React, { useState } from "react";
import { Listing } from "@/layouts/dashboard/details/basic";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "@/components/common/ui/tabs";
import { Users, UserCheck, Shield } from "lucide-react";
import { ClientManagement } from "./client";
import { EmployeeManagement } from "./employee";
import { RoleManagement } from "./role";

export function UserManagementContainer() {
  const [activeTab, setActiveTab] = useState("clients");

  return (
    <Listing className="p-6 space-y-2">
      {/* Dashboard Header */}
      <Listing.Header
        title={{ text: "User & Role Management", size: "2xl" }}
        caption="Manage employees, clients, and roles across your organization"
      />

      {/* Tabbed Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-max grid-cols-3">
          <TabsTrigger value="clients" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Clients
          </TabsTrigger>
          <TabsTrigger value="employees" className="flex items-center gap-2">
            <UserCheck className="h-4 w-4" />
            Employees
          </TabsTrigger>
          <TabsTrigger value="roles" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Roles
          </TabsTrigger>
        </TabsList>

        <TabsContent value="clients" className="mt-6">
          <ClientManagement />
        </TabsContent>

        <TabsContent value="employees" className="mt-6">
          <EmployeeManagement />
        </TabsContent>

        <TabsContent value="roles" className="mt-6">
          <RoleManagement />
        </TabsContent>
      </Tabs>
    </Listing>
  );
}
