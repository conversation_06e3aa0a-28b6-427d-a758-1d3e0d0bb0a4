"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "@/components/common/ui/dialog";
import { Button } from "@/components/common/ui/button";

import { AlertTriangle, Users } from "lucide-react";
import { CreateEmployeeForm } from "./forms/create";
import { EditEmployeeForm } from "./forms/edit";
import { DeleteEmployeeForm } from "./forms/delete";
import type {
  User,
  CreateEmployeeSchema,
  UpdateEmployeeSchema,
} from "@/lib/api/validators/schemas/user";
import { z } from "zod";

// Create Employee Dialog Props
interface CreateEmployeeDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: z.infer<typeof CreateEmployeeSchema>) => Promise<void>;
  isCreating: boolean;
}

// Edit Employee Dialog Props
interface EditEmployeeD<PERSON><PERSON><PERSON><PERSON> {
  employee: User | null;
  isOpen: boolean;
  onSubmit: (
    id: string,
    data: Partial<z.infer<typeof UpdateEmployeeSchema>>
  ) => Promise<void>;
  onOpenChange: (open: boolean) => void;
  isUpdating: boolean;
}

// Delete Employee Dialog Props
interface DeleteEmployeeDialogProps {
  employee: User | null;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (id: string) => Promise<void>;
  isDeleting: boolean;
}

// Create Employee Dialog Component
export function CreateEmployeeDialog({
  isOpen,
  onOpenChange,
  onSubmit,
  isCreating,
}: CreateEmployeeDialogProps) {
  const handleSubmit = async (data: z.infer<typeof CreateEmployeeSchema>) => {
    try {
      await onSubmit(data);
      onOpenChange(false); // Close dialog on success
    } catch (error) {
      // Error handling is done in the parent component
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Create New Employee
          </DialogTitle>
        </DialogHeader>

        <CreateEmployeeForm onSubmit={handleSubmit}>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isCreating}>
              {isCreating ? "Creating..." : "Create Employee"}
            </Button>
          </DialogFooter>
        </CreateEmployeeForm>
      </DialogContent>
    </Dialog>
  );
}

// Edit Employee Dialog Component
export function EditEmployeeDialog({
  employee,
  isOpen,
  onOpenChange,
  onSubmit,
  isUpdating,
}: EditEmployeeDialogProps) {
  if (!employee) return null;

  const handleSubmit = async (
    id: string,
    data: Partial<z.infer<typeof UpdateEmployeeSchema>>
  ) => {
    try {
      await onSubmit(id, data);
      onOpenChange(false); // Close dialog on success
    } catch (error) {
      // Error handling is done in the parent component
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Edit Employee: {employee.firstName} {employee.lastName}
          </DialogTitle>
        </DialogHeader>

        <EditEmployeeForm employee={employee} onSubmit={handleSubmit}>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isUpdating}>
              {isUpdating ? "Updating..." : "Update Employee"}
            </Button>
          </DialogFooter>
        </EditEmployeeForm>
      </DialogContent>
    </Dialog>
  );
}

// Delete Employee Dialog Component
export function DeleteEmployeeDialog({
  employee,
  isOpen,
  onOpenChange,
  onConfirm,
  isDeleting,
}: DeleteEmployeeDialogProps) {
  if (!employee) return null;

  const handleConfirm = async () => {
    try {
      await onConfirm(employee.id);
      onOpenChange(false);
    } catch (error) {
      // Error handling is done in the parent component
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
            Delete Employee: {employee.firstName} {employee.lastName}
          </DialogTitle>
        </DialogHeader>

        <DeleteEmployeeForm
          employee={employee}
          onSuccess={() => onOpenChange(false)}
        >
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete Employee"}
            </Button>
          </DialogFooter>
        </DeleteEmployeeForm>
      </DialogContent>
    </Dialog>
  );
}
