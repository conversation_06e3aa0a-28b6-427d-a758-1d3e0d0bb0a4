"use client";

import React, { useState, useEffect } from "react";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";
import { toast } from "sonner";
import { useRoles } from "@/hooks/useRoles";
import type {
  User,
  UpdateEmployeeSchema,
} from "@/lib/api/validators/schemas/user";
import { z } from "zod";

// Edit Employee Form Props
interface EditEmployeeFormProps {
  onSubmit: (
    id: string,
    data: Partial<z.infer<typeof UpdateEmployeeSchema>>
  ) => Promise<void>;
  employee: User;
  children?: React.ReactNode;
}

// Edit Employee Form Component
export function EditEmployeeForm({
  onSubmit,
  employee,
  children,
}: EditEmployeeFormProps) {
  const [formData, setFormData] = useState<
    Partial<z.infer<typeof UpdateEmployeeSchema>>
  >({});

  // Fetch roles for the dropdown
  const { roles, isLoading: rolesLoading } = useRoles();

  // Initialize form data when employee changes
  useEffect(() => {
    if (employee) {
      setFormData({
        id: employee.id,
        firstName: employee.firstName,
        lastName: employee.lastName,
        email: employee.email,
        phone: employee.phone || "",
        status: employee.status,
        roleId: employee.roleId || "",
      });
    }
  }, [employee]);

  const handleInputChange = (
    field: keyof z.infer<typeof UpdateEmployeeSchema>,
    value: any
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formData.firstName?.trim()) {
      toast.error("First name is required");
      return;
    }

    if (!formData.lastName?.trim()) {
      toast.error("Last name is required");
      return;
    }

    if (!formData.email?.trim()) {
      toast.error("Email is required");
      return;
    }

    if (!formData.roleId?.trim()) {
      toast.error("Role is required for employees");
      return;
    }

    if (!formData.status) {
      delete formData.status;
    }

    try {
      await onSubmit(employee.id, formData);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="edit-firstName">First Name *</Label>
            <Input
              id="edit-firstName"
              value={formData.firstName || ""}
              onChange={(e) => handleInputChange("firstName", e.target.value)}
              placeholder="Enter first name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="edit-lastName">Last Name *</Label>
            <Input
              id="edit-lastName"
              value={formData.lastName || ""}
              onChange={(e) => handleInputChange("lastName", e.target.value)}
              placeholder="Enter last name"
              required
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="edit-email">Email *</Label>
          <Input
            id="edit-email"
            type="email"
            value={formData.email || ""}
            onChange={(e) => handleInputChange("email", e.target.value)}
            placeholder="Enter email address"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="edit-phone">Phone</Label>
          <Input
            id="edit-phone"
            value={formData.phone || ""}
            onChange={(e) => handleInputChange("phone", e.target.value)}
            placeholder="Enter phone number"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="edit-role">Role *</Label>
            <Select
              value={formData.roleId || ""}
              onValueChange={(value) => handleInputChange("roleId", value)}
              disabled={rolesLoading}
            >
              <SelectTrigger>
                <SelectValue
                  placeholder={
                    rolesLoading ? "Loading roles..." : "Select a role"
                  }
                />
              </SelectTrigger>
              <SelectContent>
                {roles.map((role) => (
                  <SelectItem key={role.id} value={role.id}>
                    <div className="flex flex-col">
                      <span>{role.name}</span>
                      {role.description && (
                        <span className="text-sm text-muted-foreground">
                          {role.description}
                        </span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="edit-status">Status</Label>
            <Select
              value={formData.status}
              onValueChange={(value) => handleInputChange("status", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created">Created</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {children}
    </form>
  );
}
