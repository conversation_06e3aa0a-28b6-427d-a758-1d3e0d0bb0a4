"use client";

import React from "react";

import { AlertTriangle } from "lucide-react";

import type { User } from "@/lib/api/validators/schemas/user";

// Delete Employee Form Props
interface DeleteEmployeeFormProps {
  employee: User;
  children?: React.ReactNode;
  onSuccess?: () => void;
}

// Delete Employee Form Component
export function DeleteEmployeeForm({
  employee,
  children,
  onSuccess,
}: DeleteEmployeeFormProps) {
  return (
    <div className="space-y-4">
      <div className="space-y-4">
        <p className="text-sm text-muted-foreground">
          Are you sure you want to delete the employee "{employee.firstName}{" "}
          {employee.lastName}
          "? This action cannot be undone and will permanently remove the
          employee and all associated data.
        </p>

        <div className="bg-amber-50 border border-amber-200 rounded-md p-3">
          <div className="flex items-center gap-2 text-amber-800">
            <AlertTriangle className="h-4 w-4" />
            <span className="text-sm font-medium">
              Warning: This will permanently delete the employee account.
            </span>
          </div>
        </div>

        <div className="bg-muted p-3 rounded-md">
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Name:</span>
              <span className="text-foreground font-medium">
                {employee.firstName} {employee.lastName}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Email:</span>
              <span className="text-foreground font-medium">
                {employee.email}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Type:</span>
              <span className="text-foreground font-medium capitalize">
                {employee.type}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Status:</span>
              <span className="text-foreground font-medium capitalize">
                {employee.status}
              </span>
            </div>
            {employee.roleId && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Role ID:</span>
                <span className="text-foreground font-medium">
                  {employee.roleId}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {children}
    </div>
  );
}
