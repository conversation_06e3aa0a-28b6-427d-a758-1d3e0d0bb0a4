import { z } from "zod";
import { StatusSchema, IdSchema, RequiredStringSchema } from "./common";

// Subscription status enum (using the existing status enum from common)
export const SubscriptionStatusSchema = StatusSchema;

// Sort order enum
export const SortOrderEnum = z.enum(["asc", "desc"]);

// Subscription sort fields enum
export const SubscriptionSortFieldEnum = z.enum([
  "name",
  "price",
  "subscribers",
  "status",
  "createdAt",
  "updatedAt",
]);

/**
 * Base Subscription schema based on Prisma subscription model
 * Excludes metadata fields: id, createdAt, updatedAt
 */
export const BaseSubscriptionSchema = z.object({
  name: RequiredStringSchema,
  description: z.string().optional(),
  status: SubscriptionStatusSchema.default("active"),
  features: z.array(z.any()).default([]), // Json[] in Prisma
  price: z.number().min(0, "Price must be non-negative"),
  subscribers: z
    .number()
    .int()
    .min(0, "Subscribers must be non-negative")
    .default(0),
});

/**
 * Complete Subscription schema including metadata fields
 */
export const SubscriptionSchema = BaseSubscriptionSchema.safeExtend({
  id: IdSchema,
  createdAt: z.date(),
  updatedAt: z.date(),
});

/**
 * Subscription creation schema (for API requests)
 * Excludes id, createdAt, updatedAt, and makes subscribers optional since it defaults to 0
 */
export const CreateSubscriptionSchema = BaseSubscriptionSchema.omit({
  subscribers: true,
}).safeExtend({
  subscribers: z
    .number()
    .int()
    .min(0, "Subscribers must be non-negative")
    .optional(),
});

/**
 * Subscription update schema (for API requests)
 * All fields are optional except id
 */
export const UpdateSubscriptionSchema =
  BaseSubscriptionSchema.partial().safeExtend({
    id: IdSchema,
  });

/**
 * Subscription query schema for filtering and pagination
 */
export const SubscriptionQuerySchema = z.object({
  id: z.string().optional(),
  name: z.string().optional(),
  status: SubscriptionStatusSchema.optional(),
  minPrice: z.number().min(0).optional(),
  maxPrice: z.number().min(0).optional(),
  minSubscribers: z.number().int().min(0).optional(),
  maxSubscribers: z.number().int().min(0).optional(),
  search: z.string().optional(),
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(10),
  sortBy: SubscriptionSortFieldEnum.default("createdAt"),
  sortOrder: SortOrderEnum.default("desc"),
  includeFeatures: z.boolean().default(true),
});

/**
 * Bulk subscription operations schema
 */
export const BulkSubscriptionSchema = z.object({
  action: z.enum([
    "create",
    "update",
    "delete",
    "activate",
    "deactivate",
    "updatePrice",
    "updateSubscribers",
  ]),
  subscriptionIds: z.array(z.string()).optional(),
  subscriptions: z.array(CreateSubscriptionSchema).optional(),
  updates: UpdateSubscriptionSchema.omit({ id: true }).optional(),
  batchSize: z.number().int().positive().max(100).default(50),
});

/**
 * Subscription statistics schema
 */
export const SubscriptionStatisticsSchema = z.object({
  totalSubscriptions: z.number().int().min(0),
  activeSubscriptions: z.number().int().min(0),
  inactiveSubscriptions: z.number().int().min(0),
  totalSubscribers: z.number().int().min(0),
  averagePrice: z.number().min(0),
  totalRevenue: z.number().min(0),
  subscriptionsByStatus: z.record(z.string(), z.number().int().min(0)),
  recentSubscriptions: z.array(SubscriptionSchema).max(10),
});

// Type exports for use in other files
export type Subscription = z.infer<typeof SubscriptionSchema>;
export type CreateSubscription = z.infer<typeof CreateSubscriptionSchema>;
export type UpdateSubscription = z.infer<typeof UpdateSubscriptionSchema>;
export type SubscriptionQuery = z.infer<typeof SubscriptionQuerySchema>;
export type BulkSubscription = z.infer<typeof BulkSubscriptionSchema>;
export type SubscriptionStatistics = z.infer<
  typeof SubscriptionStatisticsSchema
>;
export type SubscriptionStatus = z.infer<typeof SubscriptionStatusSchema>;
export type SubscriptionSortField = z.infer<typeof SubscriptionSortFieldEnum>;
