import { z } from "zod";
import { StatusSchema, IdSchema, RequiredStringSchema } from "./common";

// User type enum (matching Prisma schema)
export const UserTypeSchema = z.enum(["admin", "employee", "client"]);

// Sort order enum
export const SortOrderEnum = z.enum(["asc", "desc"]);

// User sort fields enum
export const UserSortFieldEnum = z.enum([
  "firstName",
  "lastName",
  "email",
  "type",
  "status",
  "createdAt",
  "updatedAt",
]);

/**
 * Base User schema based on Prisma user model
 * Excludes metadata fields: id, createdAt, updatedAt, emailVerified
 */
export const BaseUserSchema = z.object({
  firstName: z.string().min(1, "First name is required").max(50),
  lastName: z.string().min(1, "Last name is required").max(50),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional(),
  image: z.string().url().optional(),
  status: StatusSchema.default("created"),
  type: UserTypeSchema.default("client"),
  customerId: z.string().optional(),
  roleId: z.string().optional(),
  companyId: z.string().optional(),
});

/**
 * Complete User schema including metadata fields
 */
export const UserSchema = BaseUserSchema.safeExtend({
  id: IdSchema,
  emailVerified: z.date().nullable().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

/**
 * Base User creation schema (for API requests)
 * Excludes id, createdAt, updatedAt, emailVerified
 */
export const CreateUserSchema = BaseUserSchema.safeExtend({
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .optional(),
  confirmPassword: z.string().optional(),
}).refine(
  (data) => {
    // If password is provided, confirmPassword must match
    if (data.password && data.password !== data.confirmPassword) {
      return false;
    }
    return true;
  },
  {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  }
);

/**
 * Client-specific creation schema
 * Includes company and subscription fields, plus optional company creation fields
 */
export const CreateClientSchema = CreateUserSchema.safeExtend({
  type: z.literal("client"),
  companyId: z.string().optional(),
  subscriptionId: z.string().optional(),
  // Optional company creation fields (for creating company during client creation)
  companyName: z.string().optional(),
  companyTin: z.string().optional(),
  companyCategory: z.string().optional(),
  companyEmail: z.string().email().optional().or(z.literal("")),
  companyAddress: z.string().optional(),
  companyPhone: z.string().optional(),
}).omit({ roleId: true }); // Clients don't have roles

/**
 * Employee-specific creation schema
 * Includes role field, excludes company and subscription
 */
export const CreateEmployeeSchema = CreateUserSchema.safeExtend({
  type: z.enum(["admin", "employee"]),
  roleId: z.string().min(1, "Role is required for employees"),
}).omit({ companyId: true, subscriptionId: true }); // Employees don't have company/subscription

/**
 * User update schema (for API requests)
 * All fields are optional except id
 */
export const UpdateUserSchema = BaseUserSchema.partial()
  .safeExtend({
    id: IdSchema,
    password: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .optional(),
    confirmPassword: z.string().optional(),
  })
  .refine(
    (data) => {
      // If password is provided, confirmPassword must match
      if (data.password && data.password !== data.confirmPassword) {
        return false;
      }
      return true;
    },
    {
      message: "Passwords do not match",
      path: ["confirmPassword"],
    }
  );

/**
 * Client-specific update schema
 * Includes company and subscription fields
 */
export const UpdateClientSchema = UpdateUserSchema.safeExtend({
  companyId: z.string().optional(),
  subscriptionId: z.string().optional(),
}).omit({ roleId: true, type: true }); // Clients don't change type or have roles

/**
 * Employee-specific update schema
 * Includes role field, excludes company and subscription
 */
export const UpdateEmployeeSchema = UpdateUserSchema.safeExtend({
  roleId: z.string().optional(),
}).omit({ companyId: true, subscriptionId: true, type: true }); // Employees don't change type or have company/subscription

/**
 * User query schema for filtering and pagination
 */
export const UserQuerySchema = z.object({
  id: z.string().optional(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  email: z.string().optional(),
  type: UserTypeSchema.optional(),
  status: StatusSchema.optional(),
  companyId: z.string().optional(),
  roleId: z.string().optional(),
  search: z.string().optional(),
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(10),
  sortBy: UserSortFieldEnum.default("createdAt"),
  sortOrder: SortOrderEnum.default("desc"),
  includeRole: z.boolean().default(true),
  includeCompany: z.boolean().default(true),
});

/**
 * Bulk user operations schema
 */
export const BulkUserSchema = z.object({
  action: z.enum([
    "create",
    "update",
    "delete",
    "activate",
    "deactivate",
    "changeType",
    "assignRole",
    "assignCompany",
  ]),
  userIds: z.array(z.string()).optional(),
  users: z.array(CreateUserSchema).optional(),
  updates: UpdateUserSchema.omit({ id: true }).optional(),
  targetType: UserTypeSchema.optional(),
  targetRoleId: z.string().optional(),
  targetCompanyId: z.string().optional(),
  batchSize: z.number().int().positive().max(100).default(50),
});

/**
 * User statistics schema
 */
export const UserStatisticsSchema = z.object({
  totalUsers: z.number().int().min(0),
  activeUsers: z.number().int().min(0),
  inactiveUsers: z.number().int().min(0),
  adminUsers: z.number().int().min(0),
  employeeUsers: z.number().int().min(0),
  clientUsers: z.number().int().min(0),
  usersByStatus: z.record(z.string(), z.number().int().min(0)),
  usersByType: z.record(z.string(), z.number().int().min(0)),
  recentUsers: z.array(UserSchema).max(10),
  usersWithCompanies: z.number().int().min(0),
  usersWithRoles: z.number().int().min(0),
});

// Type exports for use in other files
export type User = z.infer<typeof UserSchema>;
export type CreateUser = z.infer<typeof CreateUserSchema>;
export type CreateClient = z.infer<typeof CreateClientSchema>;
export type CreateEmployee = z.infer<typeof CreateEmployeeSchema>;
export type UpdateUser = z.infer<typeof UpdateUserSchema>;
export type UpdateClient = z.infer<typeof UpdateClientSchema>;
export type UpdateEmployee = z.infer<typeof UpdateEmployeeSchema>;
export type UserQuery = z.infer<typeof UserQuerySchema>;
export type BulkUser = z.infer<typeof BulkUserSchema>;
export type UserStatistics = z.infer<typeof UserStatisticsSchema>;
export type UserType = z.infer<typeof UserTypeSchema>;
export type UserSortField = z.infer<typeof UserSortFieldEnum>;
