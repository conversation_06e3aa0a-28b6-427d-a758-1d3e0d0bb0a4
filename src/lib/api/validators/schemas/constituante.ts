import { z } from "zod";

// Constituante status enum
export const ConstituanteStatus = z.enum([
  "active",
  "inactive",
  "pending",
  "archived",
]);

// Constituante type enum
export const ConstituanteType = z.enum([
  "primary",
  "secondary",
  "tertiary",
  "custom",
]);

// Constituante category enum
export const ConstituanteCategory = z.enum([
  "system",
  "business",
  "finance",
  "operations",
]);

// Base constituante schema
export const ConstituanteSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  type: ConstituanteType,
  category: ConstituanteCategory,
  status: ConstituanteStatus,
  metadata: z.record(z.any()).optional(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

// Schema for creating a new constituante entity
export const CreateConstituanteSchema = ConstituanteSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
}).safeExtend({
  status: ConstituanteStatus.default("active"),
});

// Schema for updating an existing constituante entity
export const UpdateConstituanteSchema = ConstituanteSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
}).partial();

// Schema for constituante query parameters
export const ConstituanteQuerySchema = z.object({
  status: ConstituanteStatus.optional(),
  type: ConstituanteType.optional(),
  category: ConstituanteCategory.optional(),
  limit: z.coerce.number().min(1).max(100).optional(),
  offset: z.coerce.number().min(0).optional(),
  search: z.string().optional(),
});

// Type exports
export type Constituante = z.infer<typeof ConstituanteSchema>;
export type CreateConstituante = z.infer<typeof CreateConstituanteSchema>;
export type UpdateConstituante = z.infer<typeof UpdateConstituanteSchema>;
export type ConstituanteQuery = z.infer<typeof ConstituanteQuerySchema>;

// Export status, type, and category enums for use in components
export type ConstituanteStatusType = z.infer<typeof ConstituanteStatus>;
export type ConstituanteTypeType = z.infer<typeof ConstituanteType>;
export type ConstituanteCategoryType = z.infer<typeof ConstituanteCategory>;
