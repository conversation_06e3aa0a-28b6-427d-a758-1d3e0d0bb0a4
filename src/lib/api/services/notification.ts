import { z } from "zod";
import webpush from "web-push";
import { BaseService, ServiceResponse, ServiceContext } from "./base";
import { prisma } from "@/lib/common/prisma";
import {
  sendNotificationToUser,
  sendNotificationToContext,
  NotificationSocketData,
  isPusherConfigured,
  initializePusherServer,
} from "@/lib/socket/server";

// Configure web-push with VAPID keys
if (process.env.VAPID_PUBLIC_KEY && process.env.VAPID_PRIVATE_KEY) {
  webpush.setVapidDetails(
    "mailto:" + (process.env.VAPID_EMAIL || "<EMAIL>"),
    process.env.VAPID_PUBLIC_KEY,
    process.env.VAPID_PRIVATE_KEY
  );
}

// Validation schemas
export const PushSubscriptionSchema = z.object({
  userId: z.string().cuid(),
  endpoint: z.string().url(),
  p256dhKey: z.string(),
  authKey: z.string(),
});

export const PushNotificationPayloadSchema = z.object({
  title: z.string().min(1).max(255),
  body: z.string().min(1).max(1000),
  icon: z.string().optional(),
  badge: z.string().optional(),
  image: z.string().url().optional(),
  tag: z.string().optional(),
  data: z.record(z.string(), z.any()).optional(),
  actions: z
    .array(
      z.object({
        action: z.string(),
        title: z.string(),
        icon: z.string().optional(),
      })
    )
    .optional(),
  requireInteraction: z.boolean().optional(),
  silent: z.boolean().optional(),
  options: z.record(z.string(), z.any()).optional(),
});

export const SendPushRequestSchema = z.object({
  userId: z.string().cuid().optional(),
  userIds: z.array(z.string().cuid()).optional(),
  payload: PushNotificationPayloadSchema,
  sendToAll: z.boolean().optional(),
});

export const NotificationLogSchema = z.object({
  type: z.enum(["push", "email", "sms", "in-app"]),
  title: z.string().min(1).max(255),
  body: z.string().min(1).max(1000),
  payload: z.string().optional(),
  recipientCount: z.number().int().min(0).default(0),
  successCount: z.number().int().min(0).default(0),
  failureCount: z.number().int().min(0).default(0),
  sentBy: z.string().cuid(),
});

// User notification schemas
export const NotificationSchema = z.object({
  title: z.string().min(1).max(255),
  message: z.string().min(1).max(1000),
  type: z.enum([
    "room",
    "chat",
    "proposal",
    "contract",
    "systemAlerts",
    "roleChanges",
    "weeklyDigest",
  ]),
  category: z.enum(["email", "push", "inApp"]),
  userId: z.string().cuid(),
  data: z.record(z.string(), z.any()).optional(),
  priority: z.enum(["low", "normal", "high", "urgent"]).default("normal"),
  expiresAt: z.date().optional(),
  sourceId: z.string().optional(),
  sourceType: z.string().optional(),
});

export const NotificationUpdateSchema = z.object({
  isRead: z.boolean().optional(),
  isDeleted: z.boolean().optional(),
  readAt: z.date().optional().nullable(),
  deletedAt: z.date().optional().nullable(),
});

export const NotificationQuerySchema = z.object({
  userId: z.string().cuid().optional(),
  type: z.string().optional(),
  category: z.string().optional(),
  isRead: z.boolean().optional(),
  isDeleted: z.boolean().optional(),
  priority: z.string().optional(),
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  sortBy: z.enum(["createdAt", "updatedAt", "priority"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

// Type definitions
export type PushSubscriptionData = z.infer<typeof PushSubscriptionSchema>;
export type PushNotificationPayload = z.infer<
  typeof PushNotificationPayloadSchema
>;
export type SendPushRequest = z.infer<typeof SendPushRequestSchema>;
export type NotificationLogData = z.infer<typeof NotificationLogSchema>;
export type NotificationData = z.infer<typeof NotificationSchema>;
export type NotificationUpdateData = z.infer<typeof NotificationUpdateSchema>;
export type NotificationQuery = z.infer<typeof NotificationQuerySchema>;

export interface PushNotificationResult {
  success: boolean;
  userId: string;
  userEmail?: string;
  error?: string;
}

export interface SendNotificationResponse {
  success: boolean;
  message: string;
  stats: {
    total: number;
    successful: number;
    failed: number;
  };
  results: PushNotificationResult[];
  logId?: string;
}

/**
 * Notification Service
 *
 * Handles both push subscriptions and notification logging
 * Provides unified interface for managing push notifications
 */
export class NotificationService extends BaseService {
  constructor() {
    super({
      enableLogging: true,
      throwOnError: false,
      validateInput: true,
      validateOutput: false,
    });
  }

  /**
   * Initialize service with context and set up models
   */
  async initialize(context: ServiceContext): Promise<void> {
    this.setContext(context);
    // this.requireAuth(); // Require authentication for all operations

    // Ensure Pusher is configured and initialized for socket notifications
    this.ensurePusherInitialized();
  }

  /**
   * Ensure Pusher server is initialized for socket notifications
   */
  private ensurePusherInitialized(): void {
    try {
      if (!isPusherConfigured()) {
        this.log(
          "warn",
          "Pusher environment variables not configured, socket notifications will not work"
        );
        return;
      }

      // Initialize Pusher server
      const pusher = initializePusherServer();
      this.log("info", "Pusher server initialized for notifications", {
        initialized: !!pusher,
      });
    } catch (error) {
      this.log(
        "error",
        "Failed to initialize Pusher server for notifications",
        {
          error: error instanceof Error ? error.message : String(error),
        }
      );
    }
  }

  // ==================== PUSH SUBSCRIPTION MANAGEMENT ====================

  /**
   * Create or update a push subscription for a user
   */
  async upsertPushSubscription(
    data: PushSubscriptionData
  ): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      const validatedData = this.validateInput(PushSubscriptionSchema, data);

      this.setModel((prisma as any).pushSubscription);

      const subscription = await this.upsertRecord({
        where: { userId: validatedData.userId },
        create: validatedData,
        update: {
          endpoint: validatedData.endpoint,
          p256dhKey: validatedData.p256dhKey,
          authKey: validatedData.authKey,
        },
        include: {
          user: {
            select: { id: true, firstName: true, lastName: true, email: true },
          },
        },
      });

      return subscription;
    }, "upsertPushSubscription");
  }

  /**
   * Get push subscription for a user
   */
  async getPushSubscription(userId: string): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      this.setModel((prisma as any).pushSubscription);

      const subscription = await this.findUniqueRecord({
        where: { userId },
        include: {
          user: {
            select: { id: true, firstName: true, lastName: true, email: true },
          },
        },
      });

      return subscription;
    }, "getPushSubscription");
  }

  /**
   * Get all push subscriptions with optional filtering
   */
  async getPushSubscriptions(
    userIds?: string[]
  ): Promise<ServiceResponse<any[]>> {
    return this.executeOperation(async () => {
      this.setModel((prisma as any).pushSubscription);

      const whereClause = userIds ? { userId: { in: userIds } } : {};

      const subscriptions = await this.findManyRecords({
        where: whereClause,
        include: {
          user: {
            select: { id: true, firstName: true, lastName: true, email: true },
          },
        },
      });

      return subscriptions;
    }, "getPushSubscriptions");
  }

  /**
   * Delete a push subscription
   */
  async deletePushSubscription(userId: string): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      this.setModel((prisma as any).pushSubscription);

      const subscription = await this.deleteRecord({
        where: { userId },
      });

      return subscription;
    }, "deletePushSubscription");
  }

  /**
   * Remove invalid push subscription (called when push fails with 410/404)
   */
  async removeInvalidSubscription(
    subscriptionId: string
  ): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      this.setModel((prisma as any).pushSubscription);

      const subscription = await this.deleteRecord({
        where: { id: subscriptionId },
      });

      this.log("info", `Removed invalid push subscription: ${subscriptionId}`);
      return subscription;
    }, "removeInvalidSubscription");
  }

  // ==================== PUSH NOTIFICATION SENDING ====================

  /**
   * Send push notification to specific users or all users
   */
  async sendPushNotification(
    request: SendPushRequest
  ): Promise<ServiceResponse<SendNotificationResponse>> {
    return this.executeOperation(async () => {
      const validatedRequest = this.validateInput(
        SendPushRequestSchema,
        request
      );
      const { userId, userIds, payload, sendToAll } = validatedRequest;

      // Check VAPID configuration
      if (!process.env.VAPID_PUBLIC_KEY || !process.env.VAPID_PRIVATE_KEY) {
        throw new Error("VAPID keys not configured");
      }

      // Get subscriptions based on request parameters
      const subscriptionsResponse = await this.getTargetSubscriptions(
        userId,
        userIds,
        sendToAll
      );
      if (!subscriptionsResponse.success || !subscriptionsResponse.data) {
        throw new Error("Failed to get push subscriptions");
      }

      const subscriptions = subscriptionsResponse.data;
      if (subscriptions.length === 0) {
        throw new Error("No push subscriptions found for specified users");
      }

      // Prepare notification payload
      const notificationPayload = this.prepareNotificationPayload(payload);

      // Send push notifications
      const results = await this.sendToSubscriptions(
        subscriptions,
        notificationPayload
      );

      // Process results
      const successful = results.filter((result) => result.success).length;
      const failed = results.length - successful;

      // Log notification for audit trail
      const logResponse = await this.logNotification({
        type: "push",
        title: payload.title,
        body: payload.body,
        payload: JSON.stringify(notificationPayload),
        recipientCount: subscriptions.length,
        successCount: successful,
        failureCount: failed,
        sentBy: this.getCurrentUserId()!,
      });

      const response: SendNotificationResponse = {
        success: true,
        message: `Push notifications sent`,
        stats: {
          total: subscriptions.length,
          successful,
          failed,
        },
        results,
        logId: logResponse.success ? logResponse.data?.id : undefined,
      };

      return response;
    }, "sendPushNotification");
  }

  /**
   * Get target subscriptions based on request parameters
   */
  private async getTargetSubscriptions(
    userId?: string,
    userIds?: string[],
    sendToAll?: boolean
  ): Promise<ServiceResponse<any[]>> {
    if (sendToAll) {
      return this.getPushSubscriptions();
    } else if (userIds && userIds.length > 0) {
      return this.getPushSubscriptions(userIds);
    } else if (userId) {
      const subscription = await this.getPushSubscription(userId);
      return {
        ...subscription,
        data: subscription.data ? [subscription.data] : [],
      };
    } else {
      throw new Error("No recipients specified");
    }
  }

  /**
   * Prepare notification payload with defaults
   */
  private prepareNotificationPayload(payload: PushNotificationPayload): any {
    return {
      title: payload.title,
      body: payload.body,
      icon: payload.icon || "/favicons/android-chrome-192x192.png",
      badge: payload.badge || "/favicons/favicon-32x32.png",
      image: payload.image,
      tag: payload.tag || `notification-${Date.now()}`,
      data: {
        timestamp: Date.now(),
        ...payload.data,
      },
      actions: payload.actions || [
        {
          action: "view",
          title: "View",
          icon: "/favicons/favicon-16x16.png",
        },
        {
          action: "dismiss",
          title: "Dismiss",
        },
      ],
      requireInteraction: payload.requireInteraction || false,
      silent: payload.silent || false,
      ...payload.options,
    };
  }

  /**
   * Send notifications to multiple subscriptions
   */
  private async sendToSubscriptions(
    subscriptions: any[],
    notificationPayload: any
  ): Promise<PushNotificationResult[]> {
    const results = await Promise.allSettled(
      subscriptions.map(async (subscription) => {
        try {
          const pushSubscription = {
            endpoint: subscription.endpoint,
            keys: {
              p256dh: subscription.p256dhKey,
              auth: subscription.authKey,
            },
          };

          await webpush.sendNotification(
            pushSubscription,
            JSON.stringify(notificationPayload),
            {
              TTL: 24 * 60 * 60, // 24 hours
              urgency: "normal",
              topic: notificationPayload.tag,
            }
          );

          return {
            success: true,
            userId: subscription.userId,
            userEmail: subscription.user?.email,
          };
        } catch (error: any) {
          this.log(
            "error",
            `Failed to send push notification to user ${subscription.userId}`,
            { error }
          );

          // Handle invalid subscriptions
          if (error.statusCode === 410 || error.statusCode === 404) {
            // Remove invalid subscription
            await this.removeInvalidSubscription(subscription.id);
          }

          return {
            success: false,
            userId: subscription.userId,
            userEmail: subscription.user?.email,
            error: error.message,
          };
        }
      })
    );

    return results.map((result) =>
      result.status === "fulfilled"
        ? result.value
        : {
            success: false,
            userId: "unknown",
            error: "Promise rejected",
          }
    );
  }

  // ==================== NOTIFICATION LOGGING ====================

  /**
   * Log a notification for audit trail
   */
  async logNotification(
    data: NotificationLogData
  ): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      const validatedData = this.validateInput(NotificationLogSchema, data);

      this.setModel((prisma as any).notificationLog);

      const log = await this.createRecord({
        data: {
          ...validatedData,
          sentAt: new Date(),
        },
        include: {
          user: {
            select: { id: true, firstName: true, lastName: true, email: true },
          },
        },
      });

      return log;
    }, "logNotification");
  }

  /**
   * Get notification logs with optional filtering
   */
  async getNotificationLogs(filters?: {
    type?: string;
    sentBy?: string;
    startDate?: Date;
    endDate?: Date;
    page?: number;
    limit?: number;
  }): Promise<ServiceResponse<any[]>> {
    return this.executeOperation(
      async () => {
        this.setModel((prisma as any).notificationLog);

        // Build where clause
        const whereClause: any = {};
        if (filters?.type) whereClause.type = filters.type;
        if (filters?.sentBy) whereClause.sentBy = filters.sentBy;
        if (filters?.startDate || filters?.endDate) {
          whereClause.sentAt = {};
          if (filters.startDate) whereClause.sentAt.gte = filters.startDate;
          if (filters.endDate) whereClause.sentAt.lte = filters.endDate;
        }

        // Set pagination
        const paginationSettings = {
          page: filters?.page,
          limit: filters?.limit,
        };
        this.handlePagination(paginationSettings);

        const logs = await this.findManyRecords({
          where: whereClause,
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
          orderBy: { sentAt: "desc" },
          skip: this.offset,
          take: this.limit,
        });

        return logs;
      },
      "getNotificationLogs",
      true
    );
  }

  /**
   * Get notification statistics
   */
  async getNotificationStats(filters?: {
    type?: string;
    sentBy?: string;
    startDate?: Date;
    endDate?: Date;
  }): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      this.setModel((prisma as any).notificationLog);

      // Build where clause
      const whereClause: any = {};
      if (filters?.type) whereClause.type = filters.type;
      if (filters?.sentBy) whereClause.sentBy = filters.sentBy;
      if (filters?.startDate || filters?.endDate) {
        whereClause.sentAt = {};
        if (filters.startDate) whereClause.sentAt.gte = filters.startDate;
        if (filters.endDate) whereClause.sentAt.lte = filters.endDate;
      }

      // Get aggregated stats using raw query
      const stats: any[] = await this.executeRawQuery`
        SELECT
          COUNT(*) as totalNotifications,
          SUM(recipientCount) as totalRecipients,
          SUM(successCount) as totalSuccessful,
          SUM(failureCount) as totalFailed,
          AVG(successCount::float / NULLIF(recipientCount, 0)) as averageSuccessRate,
          type,
          COUNT(*) as countByType
        FROM "notificationLog"
        WHERE 1=1
        ${filters?.type ? `AND type = '${filters.type}'` : ""}
        ${filters?.sentBy ? `AND "sentBy" = '${filters.sentBy}'` : ""}
        ${
          filters?.startDate
            ? `AND "sentAt" >= '${filters.startDate.toISOString()}'`
            : ""
        }
        ${
          filters?.endDate
            ? `AND "sentAt" <= '${filters.endDate.toISOString()}'`
            : ""
        }
        GROUP BY type
      `;

      return {
        overview: stats[0] || {
          totalNotifications: 0,
          totalRecipients: 0,
          totalSuccessful: 0,
          totalFailed: 0,
          averageSuccessRate: 0,
        },
        byType: stats,
      };
    }, "getNotificationStats");
  }

  /**
   * Delete old notification logs (cleanup)
   */
  async cleanupOldLogs(
    olderThanDays: number = 90
  ): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      this.setModel((prisma as any).notificationLog);

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      const result = await this.deleteManyRecords({
        where: {
          sentAt: {
            lt: cutoffDate,
          },
        },
      });

      this.log(
        "info",
        `Cleaned up ${result.count} old notification logs older than ${olderThanDays} days`
      );
      return result;
    }, "cleanupOldLogs");
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Check if VAPID is properly configured
   */
  isVapidConfigured(): boolean {
    return !!(process.env.VAPID_PUBLIC_KEY && process.env.VAPID_PRIVATE_KEY);
  }

  /**
   * Get VAPID public key for client-side subscription
   */
  getVapidPublicKey(): string | null {
    return process.env.VAPID_PUBLIC_KEY || null;
  }

  /**
   * Validate push subscription endpoint
   */
  validatePushEndpoint(endpoint: string): boolean {
    try {
      const url = new URL(endpoint);
      return ["https:", "http:"].includes(url.protocol);
    } catch {
      return false;
    }
  }

  // ==================== USER NOTIFICATION CRUD OPERATIONS ====================

  /**
   * Create a new notification for a user
   */
  async createNotification(
    data: NotificationData
  ): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      const validatedData = this.validateInput(NotificationSchema, data);

      this.setModel((prisma as any).notification);

      const notification: any = await this.createRecord({
        data: {
          ...validatedData,
          data: validatedData.data ? JSON.stringify(validatedData.data) : null,
        },
        include: {
          user: {
            select: { id: true, firstName: true, lastName: true, email: true },
          },
        },
      });

      // Send real-time notification via Socket.IO
      if (notification) {
        const socketData: NotificationSocketData = {
          id: notification.id,
          title: notification.title,
          message: notification.message,
          type: notification.type,
          category: notification.category,
          userId: notification.userId,
          data: validatedData.data,
          priority: notification.priority,
          createdAt: notification.createdAt,
        };

        this.log("info", "Attempting to send socket notification", {
          userId: notification.user.id,
          notificationId: notification.id,
          title: notification.title,
        });

        // Ensure Pusher is initialized before sending
        this.ensurePusherInitialized();

        // Send to specific user
        const socketSent = sendNotificationToUser(
          notification.user.id,
          socketData
        );

        this.log(
          "info",
          `Notification created and socket sent: ${
            socketSent ? "success" : "failed"
          }`,
          {
            userId: notification.user.id,
            notificationId: notification.id,
            socketSent,
          }
        );
      }

      return notification;
    }, "createNotification");
  }

  /**
   * Get notifications for a user with filtering and pagination
   */
  async getNotifications(
    query: NotificationQuery
  ): Promise<ServiceResponse<any[]>> {
    return this.executeOperation(
      async () => {
        const validatedQuery = this.validateInput(
          NotificationQuerySchema,
          query
        );

        this.setModel((prisma as any).notification);
        const userId = this.getCurrentUserId();

        // Build where clause
        const whereClause: any = {};

        if (validatedQuery.userId) {
          whereClause.userId = validatedQuery.userId;
        } else if (userId) {
          whereClause.userId = userId;
        }

        if (validatedQuery.type) whereClause.type = validatedQuery.type;
        if (validatedQuery.category)
          whereClause.category = validatedQuery.category;
        if (validatedQuery.priority)
          whereClause.priority = validatedQuery.priority;
        if (typeof validatedQuery.isRead === "boolean")
          whereClause.isRead = validatedQuery.isRead;
        if (typeof validatedQuery.isDeleted === "boolean")
          whereClause.isDeleted = validatedQuery.isDeleted;

        // Add expiration filter - exclude expired notifications
        whereClause.OR = [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } },
        ];

        // Set pagination
        const paginationSettings = {
          page: validatedQuery.page,
          limit: validatedQuery.limit,
        };
        this.handlePagination(paginationSettings);

        const notifications = await this.findManyRecords({
          where: whereClause,
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
          orderBy: { [validatedQuery.sortBy]: validatedQuery.sortOrder },
          skip: this.offset,
          take: this.limit,
        });

        // Parse JSON data field
        const parsedNotifications = notifications.map((notification: any) => ({
          ...notification,
          data: notification.data ? JSON.parse(notification.data) : null,
        }));

        return parsedNotifications;
      },
      "getNotifications",
      true
    );
  }

  /**
   * Get a single notification by ID
   */
  async getNotification(
    id: string,
    userId?: string
  ): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      this.setModel((prisma as any).notification);

      const whereClause: any = { id };
      if (userId) whereClause.userId = userId;

      const notification = await this.findUniqueRecord({
        where: whereClause,
        include: {
          user: {
            select: { id: true, firstName: true, lastName: true, email: true },
          },
        },
      });

      if (notification) {
        // Parse JSON data field
        notification.data = notification.data
          ? JSON.parse(notification.data)
          : null;
      }

      return notification;
    }, "getNotification");
  }

  /**
   * Update a notification (mark as read, delete, etc.)
   */
  async updateNotification(
    id: string,
    data: NotificationUpdateData,
    userId?: string
  ): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      const validatedData = this.validateInput(NotificationUpdateSchema, data);

      this.setModel((prisma as any).notification);

      const whereClause: any = { id };
      if (userId) whereClause.userId = userId;

      // Prepare update data
      const updateData: any = { ...validatedData };

      // Set timestamps based on status changes
      if (validatedData.isRead === true && !validatedData.readAt) {
        updateData.readAt = new Date();
      }
      if (validatedData.isDeleted === true && !validatedData.deletedAt) {
        updateData.deletedAt = new Date();
      }

      const notification = await this.updateRecord({
        where: whereClause,
        data: updateData,
        include: {
          user: {
            select: { id: true, firstName: true, lastName: true, email: true },
          },
        },
      });

      if (notification) {
        // Parse JSON data field
        notification.data = notification.data
          ? JSON.parse(notification.data)
          : null;
      }

      return notification;
    }, "updateNotification");
  }

  /**
   * Delete a notification permanently
   */
  async deleteNotification(
    id: string,
    userId?: string
  ): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      this.setModel((prisma as any).notification);

      const whereClause: any = { id };
      if (userId) whereClause.userId = userId;

      const notification = await this.deleteRecord({
        where: whereClause,
      });

      return notification;
    }, "deleteNotification");
  }

  /**
   * Mark notification as read
   */
  async markAsRead(id: string, userId?: string): Promise<ServiceResponse<any>> {
    return this.updateNotification(
      id,
      {
        isRead: true,
        readAt: new Date(),
      },
      userId
    );
  }

  /**
   * Mark notification as unread
   */
  async markAsUnread(
    id: string,
    userId?: string
  ): Promise<ServiceResponse<any>> {
    return this.updateNotification(
      id,
      {
        isRead: false,
        readAt: null,
      },
      userId
    );
  }

  /**
   * Mark all notifications as read for a user
   */
  async markAllAsRead(userId: string): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      this.setModel((prisma as any).notification);

      const result = await (prisma as any).notification.updateMany({
        where: {
          userId,
          isRead: false,
          isDeleted: false,
        },
        data: {
          isRead: true,
          readAt: new Date(),
        },
      });

      this.log(
        "info",
        `Marked ${result.count} notifications as read for user ${userId}`
      );
      return result;
    }, "markAllAsRead");
  }

  /**
   * Soft delete notification (mark as deleted)
   */
  async softDeleteNotification(
    id: string,
    userId?: string
  ): Promise<ServiceResponse<any>> {
    return this.updateNotification(
      id,
      {
        isDeleted: true,
        deletedAt: new Date(),
      },
      userId
    );
  }

  /**
   * Get notification counts for a user
   */
  async getNotificationCounts(userId: string): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      this.setModel((prisma as any).notification);

      const counts: any[] = await this.executeRawQuery`
        SELECT
          COUNT(*) as total,
          COUNT(CASE WHEN "isRead" = false AND "isDeleted" = false THEN 1 END) as unread,
          COUNT(CASE WHEN "isRead" = true AND "isDeleted" = false THEN 1 END) as read,
          COUNT(CASE WHEN "isDeleted" = true THEN 1 END) as deleted,
          COUNT(CASE WHEN "priority" = 'urgent' AND "isRead" = false AND "isDeleted" = false THEN 1 END) as urgent
        FROM "notification"
        WHERE "userId" = ${userId}
        AND ("expiresAt" IS NULL OR "expiresAt" > NOW())
      `;

      return (
        counts[0] || {
          total: 0,
          unread: 0,
          read: 0,
          deleted: 0,
          urgent: 0,
        }
      );
    }, "getNotificationCounts");
  }

  /**
   * Create and send notification to specific user with Socket.IO
   */
  async createAndSendNotification(
    data: NotificationData
  ): Promise<ServiceResponse<any>> {
    const result = await this.createNotification(data);

    if (result.success && result.data) {
      const socketData: NotificationSocketData = {
        id: (result.data as any).id,
        title: (result.data as any).title,
        message: (result.data as any).message,
        type: (result.data as any).type,
        category: (result.data as any).category,
        userId: (result.data as any).userId,
        data: data.data,
        priority: (result.data as any).priority,
        createdAt: (result.data as any).createdAt,
      };

      const socketSent = sendNotificationToUser(
        (result.data as any).userId,
        socketData
      );
      this.log(
        "info",
        `Socket notification sent: ${socketSent ? "success" : "failed"}`
      );
    }

    return result;
  }

  /**
   * Create and send notification to context (contract, chat, etc.)
   */
  async createAndSendContextNotification(
    data: NotificationData,
    contextType: string,
    contextId: string,
    excludeUserId?: string
  ): Promise<ServiceResponse<any>> {
    const result = await this.createNotification(data);

    if (result.success && result.data) {
      const socketData: NotificationSocketData = {
        id: (result.data as any).id,
        title: (result.data as any).title,
        message: (result.data as any).message,
        type: (result.data as any).type,
        category: (result.data as any).category,
        userId: (result.data as any).userId,
        data: data.data,
        priority: (result.data as any).priority,
        createdAt: (result.data as any).createdAt,
      };

      const socketSent = sendNotificationToContext(
        contextType,
        contextId,
        socketData,
        excludeUserId
      );

      this.log(
        "info",
        `Context notification sent to ${contextType}:${contextId}: ${
          socketSent ? "success" : "failed"
        }`
      );
    }

    return result;
  }

  /**
   * Send notification to multiple users in a context
   */
  async createAndSendMultiUserNotification(
    userIds: string[],
    notificationData: Omit<NotificationData, "userId">,
    contextType?: string,
    contextId?: string
  ): Promise<ServiceResponse<any[]>> {
    return this.executeOperation(async () => {
      const results = [];

      for (const userId of userIds) {
        const data = { ...notificationData, userId };

        if (contextType && contextId) {
          const result = await this.createAndSendContextNotification(
            data,
            contextType,
            contextId
          );
          results.push(result);
        } else {
          const result = await this.createAndSendNotification(data);
          results.push(result);
        }
      }

      return results;
    }, "createAndSendMultiUserNotification");
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
