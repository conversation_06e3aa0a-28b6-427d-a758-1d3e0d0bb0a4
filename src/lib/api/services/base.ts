import { Zod<PERSON><PERSON>, ZodError } from "zod";
import { NextRequest } from "next/server";
import type { Session } from "next-auth";
import { prisma } from "@/lib/common/prisma";
import { Pagination } from "@/lib/pagination";

/**
 * Base configuration interface for all services
 */
export interface BaseServiceConfig {
  enableLogging?: boolean;
  throwOnError?: boolean;
  validateInput?: boolean;
  validateOutput?: boolean;
}

/**
 * Standard service response interface
 */
export interface ServiceResponse<T = any> {
  success: boolean;
  error: boolean;
  data?: T;
  message?: string;
  errors?: ValidationError[];
  statusCode?: number;
  timestamp?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    nextCursor?: string | null;
    previousCursor?: string | null;
  };
}

/**
 * Validation error interface
 */
export interface ValidationError {
  path: (string | number)[];
  message: string;
  code: string;
}

/**
 * Service context interface for request handling
 */
export interface ServiceContext {
  session?: Session;
  user?: Session["user"];
  request?: NextRequest;
  metadata?: Record<string, any>;
}

/**
 * Service model interface for Prisma model delegates
 */
export interface ServiceModel extends Record<string, unknown> {
  // Prisma model methods
  create?: (args: any) => Promise<any>;
  findUnique?: (args: any) => Promise<any>;
  findFirst?: (args: any) => Promise<any>;
  findMany?: (args: any) => Promise<any>;
  update?: (args: any) => Promise<any>;
  upsert?: (args: any) => Promise<any>;
  delete?: (args: any) => Promise<any>;
  count?: (args: any) => Promise<number>;
  deleteMany?: (args: any) => Promise<any>;
}

enum DatabaseMethods {
  CREATE = "create",
  FIND_OR_CREATE = "findOrCreate",
  CREATE_MANY = "createMany",
  FIND_UNIQUE = "findUnique",
  FIND_FIRST = "findFirst",
  FIND_MANY = "findMany",
  UPDATE = "update",
  UPSERT = "upsert",
  DELETE = "delete",
  COUNT = "count",
  DELETE_MANY = "deleteMany",
}

export interface DatabaseOptions {
  // Common query options
  where?: Record<string, unknown>;
  include?: Record<string, unknown>;
  select?: Record<string, unknown>;
  orderBy?: Record<string, unknown> | Record<string, unknown>[];
  take?: number;
  skip?: number;

  // Create/Update/Upsert specific options
  data?: Record<string, unknown> | any[];
  create?: Record<string, unknown>;
  update?: Record<string, unknown>;

  // Additional Prisma options
  distinct?: string[];
  cursor?: Record<string, unknown>;
}

/**
 * Base service class providing common functionality for all services
 *
 * This abstract class provides:
 * 1. Standardized response formatting
 * 2. Error handling and logging
 * 3. Input/output validation
 * 4. Common utility methods
 * 5. Context management
 */
export abstract class BaseService extends Pagination {
  protected config: Required<BaseServiceConfig>;
  protected context?: ServiceContext;
  protected model?: ServiceModel;

  /**
   * Constructor for BaseService
   * @param config - Service configuration options
   */
  constructor(config: BaseServiceConfig = {}) {
    super();
    this.config = {
      enableLogging: config.enableLogging ?? true,
      throwOnError: config.throwOnError ?? false,
      validateInput: config.validateInput ?? true,
      validateOutput: config.validateOutput ?? false,
    };
  }

  /**
   * Set the service context
   * @param context - Service context with session, user, request data
   */
  setContext(context: ServiceContext): void {
    this.context = context;
  }

  /**
   * Get the current service context
   * @returns Current service context
   */
  getContext(): ServiceContext | undefined {
    return this.context;
  }

  /**
   * Set the service model
   * @param model - Prisma model delegate (e.g., prisma.document, prisma.proposal)
   */
  setModel(model: ServiceModel): void {
    this.model = model;
  }

  /**
   * Get the current service model
   * @returns Current service model
   */
  getModel(): ServiceModel | undefined {
    return this.model;
  }

  /**
   * Create a successful service response
   * @param data - Response data
   * @param statusCode - HTTP status code
   * @param message - Optional success message
   * @param includePagination - Whether to include pagination data from the service
   * @returns Formatted success response
   */
  protected createSuccessResponse<T>(
    data: T,
    statusCode: number = 200,
    message?: string,
    includePagination: boolean = false
  ): ServiceResponse<T> {
    const response: ServiceResponse<T> = {
      success: true,
      error: false,
      data,
      statusCode,
      timestamp: new Date().toISOString(),
    };

    if (message) {
      response.message = message;
    }

    if (includePagination) {
      response.pagination = {
        page: this.page,
        limit: this.limit,
        total: this.total,
        totalPages: this.totalPages,
        hasNextPage: this.hasNextPage,
        hasPreviousPage: this.hasPreviousPage,
        nextCursor: this.cursor,
        previousCursor: null, // This would need to be managed separately for cursor pagination
      };
    }

    return response;
  }

  /**
   * Create a successful paginated service response
   * @param data - Response data array
   * @param statusCode - HTTP status code
   * @param message - Optional success message
   * @returns Formatted success response with pagination
   */
  protected createPaginatedResponse<T>(
    data: T[],
    statusCode: number = 200,
    message?: string
  ): ServiceResponse<T[]> {
    return this.createSuccessResponse(data, statusCode, message, true);
  }

  /**
   * Create an error service response
   * @param error - Error message or Error object
   * @param statusCode - HTTP status code
   * @param errors - Detailed validation errors
   * @returns Formatted error response
   */
  protected createErrorResponse(
    error: string | Error,
    statusCode: number = 500,
    errors?: ValidationError[]
  ): ServiceResponse {
    const message = error instanceof Error ? error.message : error;

    if (this.config.enableLogging) {
      this.log("error", message, { statusCode, errors });
    }

    const response: ServiceResponse = {
      success: false,
      error: true,
      message,
      statusCode,
      timestamp: new Date().toISOString(),
    };

    if (errors && errors.length > 0) {
      response.errors = errors;
    }

    if (this.config.throwOnError && error instanceof Error) {
      throw error;
    }

    return response;
  }

  /**
   * Validate data against a Zod schema
   * @param schema - Zod schema for validation
   * @param data - Data to validate
   * @returns Validation result with parsed data or errors
   */
  protected validateData<T>(
    schema: ZodType<T>,
    data: unknown
  ): { isValid: boolean; data?: T; errors?: ValidationError[] } {
    try {
      const result = schema.safeParse(data);

      if (result.success) {
        return {
          isValid: true,
          data: result.data,
        };
      } else {
        return {
          isValid: false,
          errors: this.formatZodErrors(result.error),
        };
      }
    } catch (error) {
      return {
        isValid: false,
        errors: [
          {
            path: [],
            message:
              error instanceof Error ? error.message : "Validation failed",
            code: "VALIDATION_ERROR",
          },
        ],
      };
    }
  }

  /**
   * Format Zod validation errors
   * @param zodError - Zod error object
   * @returns Formatted validation errors
   */
  private formatZodErrors(zodError: ZodError): ValidationError[] {
    return zodError.issues.map((issue) => ({
      path: issue.path.map((p) => String(p)),
      message: issue.message,
      code: String(issue.code),
    }));
  }

  /**
   * Log messages with different levels
   * @param level - Log level (info, warn, error)
   * @param message - Log message
   * @param metadata - Additional metadata
   */
  protected log(
    level: "info" | "warn" | "error",
    message: string,
    metadata?: Record<string, unknown>
  ): void {
    if (!this.config.enableLogging) return;

    const logEntry = {
      level,
      message,
      service: this.constructor.name,
      timestamp: new Date().toISOString(),
      context: this.context
        ? {
            userId: (this.context.user as { id?: string })?.id,
            userEmail: this.context.user?.email,
          }
        : undefined,
      metadata,
    };

    switch (level) {
      case "info":
        // console.info("\n", JSON.stringify(logEntry));
        break;
      case "warn":
        // console.warn("\n", JSON.stringify(logEntry));
        break;
      case "error":
        console.error("\n", JSON.stringify(logEntry));
        break;
    }
  }

  /**
   * Execute a service operation with error handling and logging
   * @param operation - The operation to execute
   * @param operationName - Name of the operation for logging
   * @param includePagination - Whether to include pagination data in response
   * @returns Service response
   */
  protected async executeOperation<T>(
    operation: () => Promise<T>,
    operationName: string,
    includePagination: boolean = false
  ): Promise<ServiceResponse<T>> {
    try {
      this.log("info", `Starting operation: ${operationName}`);

      const result = await operation();

      this.log("info", `Operation completed successfully: ${operationName}`);
      return this.createSuccessResponse(
        result,
        200,
        undefined,
        includePagination
      );
    } catch (error) {
      this.log("error", `Operation failed: ${operationName}`, { error });
      return this.createErrorResponse(
        error instanceof Error ? error : new Error(String(error)),
        500
      );
    }
  }

  /**
   * Validate input data if validation is enabled
   * @param schema - Zod schema for validation
   * @param data - Data to validate
   * @returns Validation result or throws error
   */
  protected validateInput<T>(schema: ZodType<T>, data: unknown): T {
    if (!this.config.validateInput) {
      return data as T;
    }

    const validation = this.validateData(schema, data);

    if (!validation.isValid) {
      const error = new Error("Input validation failed");
      if (this.config.throwOnError) {
        throw error;
      }
      this.log("error", "Input validation failed", {
        errors: validation.errors,
      });
      throw error;
    }

    return validation.data!;
  }

  /**
   * Validate output data if validation is enabled
   * @param schema - Zod schema for validation
   * @param data - Data to validate
   * @returns Validation result
   */
  protected validateOutput<T>(schema: ZodType<T>, data: unknown): T {
    if (!this.config.validateOutput) {
      return data as T;
    }

    const validation = this.validateData(schema, data);

    if (!validation.isValid) {
      this.log("warn", "Output validation failed", {
        errors: validation.errors,
      });
      return data as T; // Return original data but log the warning
    }

    return validation.data!;
  }

  /**
   * Check if user has required permissions
   * @param requiredRoles - Array of required roles
   * @returns Boolean indicating if user has permission
   */
  protected hasPermission(requiredRoles: string[] = []): boolean {
    if (requiredRoles.length === 0) return true;

    const userRole = (this.context?.user as any)?.role;
    return userRole && requiredRoles.includes(userRole);
  }

  /**
   * Require authentication and optionally specific roles
   * @param requiredRoles - Array of required roles
   * @throws Error if not authenticated or insufficient permissions
   */
  protected requireAuth(requiredRoles: string[] = []): void {
    if (!this.context?.session || !this.context?.user) {
      throw new Error("Authentication required");
    }

    if (!this.hasPermission(requiredRoles)) {
      throw new Error("Insufficient permissions");
    }
  }

  /**
   * Get the current user ID
   * @returns User ID or null if not authenticated
   */
  protected getCurrentUserId(): string | null {
    return (this.context?.user as any)?.id || null;
  }

  /**
   * Get the current user email
   * @returns User email or null if not authenticated
   */
  protected getCurrentUserEmail(): string | null {
    return this.context?.user?.email || null;
  }

  /**
   * Validate that the service model is properly set with Prisma model methods
   * @throws Error if model is not available or doesn't have required methods
   */
  protected validateModel(method: DatabaseMethods): void {
    if (!this.model) {
      throw new Error(
        "Service model is not set. Call setModel() with a Prisma model delegate before using service methods."
      );
    }

    // Check if model has basic Prisma model methods
    if (typeof this.model.create !== "function") {
      throw new Error(
        "Service model must be a Prisma model delegate (e.g., prisma.document, prisma.proposal) with CRUD methods."
      );
    }

    if (!this.model[method]) {
      throw new Error(`Method ${method} not available in model`);
    }
  }

  /**
   * Utility method to simulate API delay for testing and development
   * @param ms - Milliseconds to delay
   * @returns Promise that resolves after the specified delay
   */
  protected async delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  // ==================== BASE CRUD OPERATIONS ====================

  /**
   * Generic create operation with error handling
   * @param options - Database options containing data and optional include relations
   * @returns Created record
   */
  protected async createRecord<T>(options: DatabaseOptions): Promise<T> {
    this.log("info", `Creating record`);

    const result = await this.executeQuery(DatabaseMethods.CREATE, options);

    this.log("info", `Successfully created record with ID: ${result.id}`);
    return result;
  }

  /**
   * Generic find unique operation with error handling
   * @param options - Database options containing where clause and optional include relations
   * @returns Found record or null
   */
  protected async findUniqueRecord<T>(
    options: DatabaseOptions
  ): Promise<T | null> {
    this.log("info", `Finding unique record`);

    const result = await this.executeQuery(
      DatabaseMethods.FIND_UNIQUE,
      options
    );

    if (result) {
      this.log("info", `Found record with ID: ${result.id}`);
    } else {
      this.log("info", `No record found matching criteria`);
    }

    return result;
  }

  /**
   * Generic find first operation with error handling
   * @param options - Database options containing where clause and optional include relations
   * @returns Found record or null
   */
  protected async findFirstRecord<T>(
    options: DatabaseOptions
  ): Promise<T | null> {
    this.log("info", `Finding first record`);

    const result = await this.executeQuery(DatabaseMethods.FIND_FIRST, options);

    if (result) {
      this.log("info", `Found record with ID: ${result.id}`);
    } else {
      this.log("info", `No record found matching criteria`);
    }

    return result;
  }

  /**
   * Generic find many operation with error handling
   * @param options - Database options containing query parameters
   * @returns Array of found records
   */
  protected async findManyRecords<T>(
    options: DatabaseOptions = {}
  ): Promise<T[]> {
    this.log("info", `Finding many records`);

    // Calculate pagination
    const result = await this.executeQuery(DatabaseMethods.FIND_MANY, options);

    // Apply pagination query options
    this.total = Math.ceil(result.length / this.limit);
    options.take = this.limit;
    options.skip = this.offset;

    this.log("info", `Found ${result.length} records`);
    return result;
  }

  /**
   * Generic update operation with error handling
   * @param options - Database options containing where clause, data, and optional include relations
   * @returns Updated record
   */
  protected async updateRecord<T>(options: DatabaseOptions): Promise<T> {
    this.log("info", `Updating record`);

    const result = await this.executeQuery(DatabaseMethods.UPDATE, options);

    this.log("info", `Successfully updated record with ID: ${result.id}`);
    return result;
  }

  /**
   * Generic upsert operation with error handling
   * @param options - Database options containing where clause, create data, update data, and optional include relations
   * @returns Upserted record
   */
  protected async upsertRecord<T>(options: DatabaseOptions): Promise<T> {
    this.log("info", `Upserting record`);

    const result = await this.executeQuery(DatabaseMethods.UPSERT, options);

    this.log("info", `Successfully upserted record with ID: ${result.id}`);
    return result;
  }

  /**
   * Generic delete operation with error handling
   * @param options - Database options containing where clause to identify record
   * @returns Deleted record
   */
  protected async deleteRecord<T>(options: DatabaseOptions): Promise<T> {
    this.log("info", `Deleting record`);

    const result = await this.executeQuery(DatabaseMethods.DELETE, options);

    this.log("info", `Successfully deleted record with ID: ${result.id}`);
    return result;
  }

  /**
   * Generic count operation with error handling
   * @param options - Database options containing optional where clause
   * @returns Count of records
   */
  protected async countRecords(options: DatabaseOptions = {}): Promise<number> {
    this.log("info", `Counting records`);

    const result = await this.executeQuery(DatabaseMethods.COUNT, options);

    this.log("info", `Found ${result} records matching criteria`);
    return result;
  }

  /**
   * Generic delete many operation with error handling
   * @param options - Database options containing where clause
   * @returns Delete result with count
   */
  protected async deleteManyRecords(
    options: DatabaseOptions
  ): Promise<{ count: number }> {
    this.log("info", `Deleting many records`);

    const result = await this.executeQuery(
      DatabaseMethods.DELETE_MANY,
      options
    );

    this.log("info", `Successfully deleted ${result.count} records`);
    return result;
  }

  /**
   * Generic query operation with error handling
   * @param method - Prisma method to execute
   * @param options - Query options
   * @returns Query result
   */
  protected async executeQuery(
    method: DatabaseMethods,
    options: DatabaseOptions
  ): Promise<any> {
    this.validateModel(method);

    try {
      return await (this.model as any)[method](options);
    } catch (error) {
      this.log("error", `Failed to execute query`, {
        error,
      });
      throw error;
    } finally {
      this.log("info", `Query operation completed`);
    }
  }

  protected async createManyRecords<T>(options: DatabaseOptions): Promise<T> {
    this.log("info", `Creating many records`);

    const result = await this.executeQuery(
      DatabaseMethods.CREATE_MANY,
      options
    );

    this.log("info", `Successfully created many records`);
    return result;
  }

  /**
   * Find or create operation - finds a record by unique criteria, creates if not found
   * @param options - Database options containing where clause for finding and data for creating
   * @returns Found or created record with metadata about the operation
   */
  protected async findOrCreateRecord<T>(options: {
    where: Record<string, unknown>;
    data: Record<string, unknown>;
    include?: Record<string, unknown>;
    select?: Record<string, unknown>;
  }): Promise<{ record: T; created: boolean }> {
    this.log("info", `Finding or creating record`);

    try {
      // First, try to find the existing record
      const existingRecord = await this.findUniqueRecord<T>({
        where: options.where,
        include: options.include,
        select: options.select,
      });

      if (existingRecord) {
        this.log(
          "info",
          `Found existing record with ID: ${(existingRecord as any).id}`
        );
        return {
          record: existingRecord,
          created: false,
        };
      }

      // If not found, create a new record
      this.log("info", `Record not found, creating new record`);
      const newRecord = await this.createRecord<T>({
        data: options.data,
        include: options.include,
        select: options.select,
      });

      this.log(
        "info",
        `Successfully created new record with ID: ${(newRecord as any).id}`
      );
      return {
        record: newRecord,
        created: true,
      };
    } catch (error) {
      this.log("error", `Failed to find or create record`, { error });
      throw error;
    }
  }

  /**
   * Find or create a record and return only the ID
   * @param options - Database options containing where clause for finding and data for creating
   * @returns ID of the found or created record
   */
  protected async findOrCreate(options: {
    where: Record<string, unknown>;
    data: Record<string, unknown>;
    include?: Record<string, unknown>;
    select?: Record<string, unknown>;
  }): Promise<string> {
    this.log("info", `Finding or creating record (ID only)`);

    try {
      // Use the existing findOrCreateRecord method
      const result = await this.findOrCreateRecord<{ id: string }>({
        where: options.where,
        data: options.data,
        include: options.include,
        select: options.select || { id: true },
      });

      // Unwrap the object and return just the ID
      this.log(
        "info",
        `${result.created ? "Created" : "Found"} record with ID: ${
          result.record.id
        }`
      );
      return result.record.id;
    } catch (error) {
      this.log("error", `Failed to find or create record (ID only)`, { error });
      throw error;
    }
  }

  /**
   * Get the last model row index based on createdAt date
   * This method finds the count of records created before or on a specific date,
   * which can be used as an offset for pagination when seeding data
   * @param beforeDate - Date to search before (default: current date)
   * @param createdAtField - Name of the createdAt field (default: 'createdAt')
   * @returns Number representing the index/offset of records before the specified date
   */
  protected async getLastModelRowIndex(
    beforeDate?: Date,
    createdAtField: string = "createdAt"
  ): Promise<number> {
    this.log("info", `Getting last model row index based on ${createdAtField}`);

    try {
      // Use current date if no date provided
      const searchDate = beforeDate || new Date();

      this.log(
        "info",
        `Searching for records created before: ${searchDate.toISOString()}`
      );

      // Build the where clause dynamically
      const whereClause: any = {};
      whereClause[createdAtField] = {
        lte: searchDate,
      };

      // Count records created before or on the specified date
      const count = await this.countRecords({
        where: whereClause,
      });

      this.log(
        "info",
        `Found ${count} records created before ${searchDate.toISOString()}`
      );
      return count;
    } catch (error) {
      this.log("error", `Failed to get last model row index`, {
        beforeDate: beforeDate?.toISOString(),
        createdAtField,
        error,
      });
      throw error;
    }
  }

  /**
   * Get the last model row index with dynamic date range
   * This method calculates the offset based on records created within a specific time range
   * @param daysSince - Number of days back from current date to search (default: 30)
   * @param createdAtField - Name of the createdAt field (default: 'createdAt')
   * @returns Number representing the index/offset of records within the date range
   */
  protected async getLastModelRowIndexByDays(
    daysSince: number = 30,
    createdAtField: string = "createdAt"
  ): Promise<number> {
    this.log("info", `Getting last model row index for last ${daysSince} days`);

    try {
      const currentDate = new Date();
      const sinceDate = new Date(
        currentDate.getTime() - daysSince * 24 * 60 * 60 * 1000
      );

      this.log(
        "info",
        `Searching for records created between ${sinceDate.toISOString()} and ${currentDate.toISOString()}`
      );

      // Build the where clause for date range
      const whereClause: any = {};
      whereClause[createdAtField] = {
        gte: sinceDate,
        lte: currentDate,
      };

      // Count records within the date range
      const count = await this.countRecords({
        where: whereClause,
      });

      this.log(
        "info",
        `Found ${count} records created within the last ${daysSince} days`
      );
      return count;
    } catch (error) {
      this.log("error", `Failed to get last model row index by days`, {
        daysSince,
        createdAtField,
        error,
      });
      throw error;
    }
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Get user account ID from user ID
   * This is a common operation across services that need account-based filtering
   * @param userId - The user ID to get account for
   * @returns Account object with ID and other basic fields
   * @throws Error if user or account not found
   */
  protected async getCurrentUser(): Promise<any> {
    try {
      const currentUserId = this.context?.user?.id;

      this.log("info", `Getting account for user: ${currentUserId}`);

      if (!currentUserId) {
        throw new Error("User ID is required");
      }

      // Temporarily switch to account model
      const originalModel = this.model;
      this.setModel(prisma.user as any);

      const user: any = await this.findUniqueRecord({
        where: { id: currentUserId },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          phone: true,
          customerId: true,
          company: {
            select: {
              id: true,
              name: true,
              tin: true,
              category: true,
              email: true,
              address: true,
              phone: true,
              createdAt: true,
              updatedAt: true,
            },
          },
          role: {
            select: {
              id: true,
              name: true,
              description: true,
              permissions: true,
            },
          },
          createdAt: true,
          updatedAt: true,
        },
      });

      // Restore original model
      if (originalModel) {
        this.setModel(originalModel);
      }

      if (!user) {
        throw new Error("Account not found for user");
      }

      this.log("info", `Found user: ${user.id} for user: ${currentUserId}`);
      return user;
    } catch (error) {
      this.log("error", `Failed to get user account`, { error });
      throw error;
    }
  }

  /**
   * Get user account ID from user ID
   * This is a common operation across services that need account-based filtering
   * @param userId - The user ID to get account for
   * @returns Account object with ID and other basic fields
   * @throws Error if user or account not found
   */
  protected async getUserAccount(userId?: string): Promise<any> {
    try {
      this.log("info", `Getting account for user: ${userId}`);

      if (!userId) {
        throw new Error("User ID is required");
      }

      // Temporarily switch to account model
      const originalModel = this.model;
      this.setModel(prisma.account as any);

      const account: any = await this.findFirstRecord({
        where: { userId: userId },
        select: {
          id: true,
          userId: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      // Restore original model
      if (originalModel) {
        this.setModel(originalModel);
      }

      if (!account) {
        throw new Error("Account not found for user");
      }

      this.log("info", `Found account: ${account.id} for user: ${userId}`);
      return account;
    } catch (error) {
      this.log("error", `Failed to get user account`, { userId, error });
      throw error;
    }
  }

  /**
   * Get current user's account ID from context
   * Convenience method that gets account for the currently authenticated user
   * @returns Account object for current user
   * @throws Error if no user in context or account not found
   */
  protected async getCurrentUserAccount(): Promise<any> {
    const currentUserId = this.context?.user?.id;

    if (!currentUserId) {
      throw new Error("User authentication required");
    }

    return this.getUserAccount(currentUserId);
  }

  /**
   * Execute a database transaction with error handling
   * @param operation - Single operation function to execute in transaction
   * @returns Transaction result
   */
  protected async executeTransaction<T>(
    operation: (tx: any) => Promise<T>
  ): Promise<T> {
    try {
      this.log("info", `Starting database transaction`);

      const result = await prisma.$transaction(operation);

      this.log("info", `Transaction completed successfully`);
      return result;
    } catch (error) {
      this.log("error", `Transaction failed`, { error });
      throw error;
    } finally {
      this.log("info", `Transaction operation completed`);
    }
  }

  /**
   * Execute raw SQL query with error handling
   * @param query - Raw SQL query template
   * @returns Query result
   */
  protected async executeRawQuery<T>(
    query: TemplateStringsArray,
    ...params: any[]
  ): Promise<T> {
    try {
      this.log("info", `Executing raw SQL query`);

      const result = await prisma.$queryRaw(query, ...params);

      this.log("info", `Raw query executed successfully`);
      return result as T;
    } catch (error) {
      this.log("error", `Raw query execution failed`, { error });
      throw error;
    } finally {
      this.log("info", `Raw query operation completed`);
    }
  }
}
