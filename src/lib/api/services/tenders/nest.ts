import { z } from "zod";
import { BaseService, ServiceResponse } from "../base";
import { prisma } from "@/lib/common/prisma";

/**
 * Validation schemas for nest_tender operations
 */

// Schema for creating/updating nest_tender
export const NestTenderSchema = z.object({
  ocid: z.string().min(1, "OCID is required"),
  language: z.string().optional(),
  initiation_type: z.string().optional(),
  description: z.string().optional(),
  address: z.any().optional(), // JSON field
  timeline: z.any().optional(), // JSON field
  procuring_entity: z.string().optional(),
  procuring_method: z.string().optional(),
  items: z.array(z.any()).optional(), // JSON array
  parties: z.array(z.any()).optional(), // JSON array
  category: z.string().optional(),
  status: z
    .enum([
      "online",
      "offline",
      "active",
      "inactive",
      "submitted",
      "received",
      "negotiating",
      "agreed",
      "created",
      "inprogress",
      "reviewing",
      "completed",
      "closed",
      "terminated",
      "pending",
    ])
    .optional(),
});

// Schema for updating nest_tender (includes ID)
export const UpdateNestTenderSchema = NestTenderSchema.extend({
  id: z.string().min(1, "Invalid tender ID"),
});

// Schema for query parameters
export const NestTenderQuerySchema = z.object({
  id: z.string().min(1).optional(),
  ocid: z.string().optional(),
  language: z.string().optional(),
  initiation_type: z.string().optional(),
  procuring_entity: z.string().optional(),
  procuring_method: z.string().optional(),
  category: z.string().optional(),
  status: z
    .enum([
      "online",
      "offline",
      "active",
      "inactive",
      "submitted",
      "received",
      "negotiating",
      "agreed",
      "created",
      "inprogress",
      "reviewing",
      "completed",
      "closed",
      "terminated",
      "pending",
    ])
    .optional(),
  search: z.string().optional(),
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  sortBy: z.enum(["createdAt", "updatedAt", "ocid", "description"]).optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
});

// Schema for ID validation
export const IdSchema = z.string().min(1, "Invalid ID format");

/**
 * Type definitions
 */
export type NestTenderData = z.infer<typeof NestTenderSchema>;
export type UpdateNestTenderData = z.infer<typeof UpdateNestTenderSchema>;
export type NestTenderQuery = z.infer<typeof NestTenderQuerySchema>;

/**
 * NestTenderService - CRUD operations for nest_tender model
 *
 * This service extends BaseService and provides:
 * 1. Full CRUD operations for nest_tender records
 * 2. Search and filtering capabilities
 * 3. Pagination support
 * 4. Input validation using Zod schemas
 * 5. Standardized error handling and logging
 */
export class NestTenderService extends BaseService {
  constructor(config: any = {}) {
    super({
      enableLogging: true,
      validateInput: true,
      validateOutput: false,
      ...config,
    });

    // Set the Prisma model for database operations
    this.setModel((prisma as any).nest_tender);
  }

  /**
   * Create a new nest_tender record
   * @param data - Tender data to create
   * @returns Service response with created tender
   */
  async createTender(data: NestTenderData): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      this.log("info", "Creating new nest_tender");

      // Validate input data
      const validatedData = this.validateInput(NestTenderSchema, data);

      // Check if OCID already exists
      const existingTender = await this.findUniqueRecord({
        where: { ocid: validatedData.ocid },
        select: { id: true },
      });

      if (existingTender) {
        throw new Error(
          `Tender with OCID ${validatedData.ocid} already exists`
        );
      }

      // Create the tender
      const tender = await this.createRecord({
        data: {
          ...validatedData,
          status: validatedData.status || "created",
        },
      });

      this.log(
        "info",
        `Nest tender created successfully: ${(tender as any).id}`
      );
      return tender;
    }, "createTender");
  }

  /**
   * Get tender by ID
   * @param id - Tender ID
   * @param autoSeed - Automatically seed data if no records found (default: true)
   * @returns Service response with tender data
   */
  async getTenderById(
    id: string,
    autoSeed: boolean = true
  ): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      this.log("info", `Getting nest_tender by ID: ${id}`);

      // Validate ID
      const validatedId = this.validateInput(IdSchema, id);

      // Find the tender
      let tender = await this.findUniqueRecord({
        where: { id: validatedId },
      });

      // If tender not found and autoSeed is enabled, attempt seeding
      if (!tender && autoSeed) {
        this.log("info", `Tender not found, attempting unified seeding`);

        try {
          // Create a seeder instance to handle the seeding
          const seeder = new NestTenderSeederService();
          const seedResult = await seeder.bulkSeedFromExternalApi();

          if (seedResult.success && !(seedResult as any).skipped) {
            this.log(
              "info",
              `Seeding completed: ${
                seedResult.data?.processed || 0
              } records processed`
            );

            // Try to find the tender again after seeding
            tender = await this.findUniqueRecord({
              where: { id: validatedId },
            });

            if (tender) {
              this.log(
                "info",
                `Found nest_tender after seeding: ${(tender as any).id}`
              );
              return {
                ...tender,
                seeded: true,
                seedingResults: {
                  totalProcessed: seedResult.data?.processed || 0,
                  totalCreated: seedResult.data?.created || 0,
                  totalUpdated: seedResult.data?.updated || 0,
                },
              };
            }
          } else if ((seedResult as any).skipped) {
            this.log("info", `Seeding skipped: ${seedResult.message}`);
          }
        } catch (seedError) {
          this.log(
            "warn",
            `Seeding failed: ${
              seedError instanceof Error ? seedError.message : "Unknown error"
            }`
          );
          // Continue with original error handling
        }
      }

      if (!tender) {
        throw new Error("Tender not found");
      }

      this.log("info", `Found nest_tender: ${(tender as any).id}`);
      return tender;
    }, "getTenderById");
  }

  /**
   * Get tender by OCID
   * @param ocid - Tender OCID
   * @returns Service response with tender data
   */
  async getTenderByOcid(ocid: string): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      this.log("info", `Getting nest_tender by OCID: ${ocid}`);

      // Find the tender
      const tender = await this.findUniqueRecord({
        where: { ocid },
      });

      if (!tender) {
        throw new Error("Tender not found");
      }

      this.log("info", `Found nest_tender: ${(tender as any).id}`);
      return tender;
    }, "getTenderByOcid");
  }

  /**
   * Update tender by ID
   * @param data - Updated tender data including ID
   * @returns Service response with updated tender
   */
  async updateTender(data: UpdateNestTenderData): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      this.log("info", `Updating nest_tender: ${data.id}`);

      // Validate input data
      const validatedData = this.validateInput(UpdateNestTenderSchema, data);
      const { id, ...updateData } = validatedData;

      // Check if tender exists
      const existingTender = await this.findUniqueRecord({
        where: { id },
        select: { id: true, ocid: true },
      });

      if (!existingTender) {
        throw new Error("Tender not found");
      }

      // If OCID is being updated, check for conflicts
      if (updateData.ocid && updateData.ocid !== (existingTender as any).ocid) {
        const ocidExists = await this.findUniqueRecord({
          where: {
            ocid: updateData.ocid,
            NOT: { id },
          },
          select: { id: true },
        });

        if (ocidExists) {
          throw new Error(`Tender with OCID ${updateData.ocid} already exists`);
        }
      }

      // Update the tender
      const updatedTender = await this.updateRecord({
        where: { id },
        data: {
          ...updateData,
          updatedAt: new Date(),
        },
      });

      this.log("info", `Nest tender updated successfully: ${id}`);
      return updatedTender;
    }, "updateTender");
  }

  /**
   * Delete tender by ID
   * @param id - Tender ID
   * @returns Service response with deletion result
   */
  async deleteTender(id: string): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      this.log("info", `Deleting nest_tender: ${id}`);

      // Validate ID
      const validatedId = this.validateInput(IdSchema, id);

      // Check if tender exists
      const existingTender = await this.findUniqueRecord({
        where: { id: validatedId },
        select: { id: true },
      });

      if (!existingTender) {
        throw new Error("Tender not found");
      }

      // Delete the tender
      await this.deleteRecord({
        where: { id: validatedId },
      });

      this.log("info", `Nest tender deleted successfully: ${validatedId}`);
      return { id: validatedId, deleted: true };
    }, "deleteTender");
  }

  /**
   * Get all tenders with filtering and pagination (with automatic database population)
   * @param query - Query parameters for filtering and pagination (includes autoSeed option)
   * @returns Service response with tenders list
   */
  async getTenders(
    query: NestTenderQuery & { autoSeed?: boolean } = {}
  ): Promise<ServiceResponse> {
    return this.executeOperation(
      async () => {
        this.log("info", "Getting nest_tenders with filters");

        // Extract autoSeed option (default: true)
        const { autoSeed = true, ...queryParams } = query;

        // Step 1: Attempt to populate database with latest data (unless disabled)
        if (autoSeed) {
          try {
            this.log(
              "info",
              "Attempting to populate database before fetching tenders"
            );

            // Create a seeder instance to handle the seeding
            const seeder = new NestTenderSeederService();
            const seedResult = await seeder.bulkSeedFromExternalApi();

            if (seedResult.success && !(seedResult as any).skipped) {
              this.log(
                "info",
                `Database seeding completed: ${
                  seedResult.data?.processed || 0
                } records processed`
              );
            } else if ((seedResult as any).skipped) {
              this.log(
                "info",
                `Database seeding skipped: ${seedResult.message}`
              );
            }
          } catch (error) {
            // Log seeding error but continue with the query
            const errorMessage =
              error instanceof Error ? error.message : "Unknown seeding error";
            this.log(
              "warn",
              `Database seeding failed, continuing with query: ${errorMessage}`
            );
          }
        }

        // Step 2: Validate query parameters
        const validatedQuery = this.validateInput(
          NestTenderQuerySchema,
          queryParams
        );

        // Build where clause
        const where: any = {};
        if (validatedQuery.id) where.id = validatedQuery.id;
        if (validatedQuery.ocid)
          where.ocid = { contains: validatedQuery.ocid, mode: "insensitive" };
        if (validatedQuery.language) where.language = validatedQuery.language;
        if (validatedQuery.initiation_type)
          where.initiation_type = validatedQuery.initiation_type;
        if (validatedQuery.procuring_entity)
          where.procuring_entity = {
            contains: validatedQuery.procuring_entity,
            mode: "insensitive",
          };
        if (validatedQuery.procuring_method)
          where.procuring_method = validatedQuery.procuring_method;
        if (validatedQuery.category)
          where.category = {
            contains: validatedQuery.category,
            mode: "insensitive",
          };
        if (validatedQuery.status) where.status = validatedQuery.status;

        // Handle search across multiple fields
        if (validatedQuery.search) {
          where.OR = [
            { ocid: { contains: validatedQuery.search, mode: "insensitive" } },
            {
              description: {
                contains: validatedQuery.search,
                mode: "insensitive",
              },
            },
            {
              procuring_entity: {
                contains: validatedQuery.search,
                mode: "insensitive",
              },
            },
            {
              category: {
                contains: validatedQuery.search,
                mode: "insensitive",
              },
            },
          ];
        }

        // Build order by clause
        const orderBy: any = {};
        if (validatedQuery.sortBy) {
          orderBy[validatedQuery.sortBy] = validatedQuery.sortOrder || "desc";
        } else {
          orderBy.createdAt = "desc";
        }

        // Set pagination
        const paginationSettings = {
          page: validatedQuery.page || 1,
          limit: validatedQuery.limit || 20,
        };
        this.handlePagination(paginationSettings);

        // Get tenders with pagination
        const [tenders, total] = await Promise.all([
          this.findManyRecords({
            where,
            orderBy,
            take: this.limit,
            skip: this.offset,
          }),
          this.countRecords({ where }),
        ]);

        // Update total count for pagination
        this.total = total;

        this.log(
          "info",
          `Found ${tenders.length} nest_tenders (${total} total)`
        );

        return tenders;
      },
      "getTenders",
      true
    );
  }

  /**
   * Upsert tender (create or update based on OCID)
   * @param data - Tender data
   * @returns Service response with upserted tender
   */
  async upsertTender(data: NestTenderData): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      this.log("info", `Upserting nest_tender with OCID: ${data.ocid}`);

      // Validate input data
      const validatedData = this.validateInput(NestTenderSchema, data);

      // Use Prisma's upsert functionality
      const tender = await this.upsertRecord({
        where: { ocid: validatedData.ocid },
        update: {
          ...validatedData,
          updatedAt: new Date(),
        },
        create: {
          ...validatedData,
          status: validatedData.status || "created",
        },
      });

      this.log(
        "info",
        `Nest tender upserted successfully: ${(tender as any).id}`
      );
      return tender;
    }, "upsertTender");
  }
}

/**
 * Data transformer interface for external API data
 */
interface NestApiRecord {
  ocid: string;
  compiledRelease: {
    ocid: string;
    language?: string;
    initiationType?: string;
    tender?: {
      description?: string;
      procurementMethod?: string;
      procuringEntity?: {
        name?: string;
      };
      items?: any[];
      tenderPeriod?: any;
    };
    parties?: any[];
    buyer?: {
      name?: string;
      address?: any;
    };
  };
}

interface NestApiResponse {
  records: NestApiRecord[];
}

/**
 * NestTenderSeederService - External API calls and data seeding
 *
 * This service provides:
 * 1. External API integration for fetching tender data
 * 2. Data transformation from external API format to database schema
 * 3. Bulk upsert operations for seeding data
 * 4. Error handling for external API failures
 */
export class NestTenderSeederService extends NestTenderService {
  constructor(config: any = {}) {
    super({
      enableLogging: true,
      validateInput: true,
      validateOutput: false,
      ...config,
    });
  }

  /**
   * Transform external API data to database schema format
   * @param apiRecord - Raw record from external API
   * @returns Transformed data matching nest_tender schema
   */
  private transformApiRecord(apiRecord: NestApiRecord): NestTenderData {
    const { ocid, compiledRelease } = apiRecord;
    const { tender, parties, buyer } = compiledRelease;

    // Extract address information
    let address = null;
    if (buyer?.address) {
      address = buyer.address;
    } else if (parties && parties.length > 0) {
      const procuringParty = parties.find((p) =>
        p.roles?.includes("procuringEntity")
      );
      if (procuringParty?.address) {
        address = procuringParty.address;
      }
    }

    // Extract timeline information from tender period
    let timeline = null;
    if (tender?.tenderPeriod) {
      timeline = {
        startDate: tender.tenderPeriod.startDate,
        endDate: tender.tenderPeriod.endDate,
        maxExtentDate: tender.tenderPeriod.maxExtentDate,
        durationInDays: tender.tenderPeriod.durationInDays,
      };
    }

    // Extract procuring entity name
    let procuring_entity: string | undefined = undefined;
    if (tender?.procuringEntity?.name) {
      procuring_entity = tender.procuringEntity.name;
    } else if (buyer?.name) {
      procuring_entity = buyer.name;
    }

    // Determine category from items
    let category: string | undefined = undefined;
    if (tender?.items && tender.items.length > 0) {
      const firstItem = tender.items[0];
      if (firstItem.classification?.description) {
        category = firstItem.classification.description;
      }
    }

    return {
      ocid,
      language: compiledRelease.language || "en",
      initiation_type: compiledRelease.initiationType || "tender",
      description: tender?.description || undefined,
      address,
      timeline,
      procuring_entity,
      procuring_method: tender?.procurementMethod || undefined,
      items: tender?.items || [],
      parties: parties || [],
      category,
      status: "active", // Default status for imported records
    };
  }

  /**
   * Seed database with data from external API response
   * @param apiData - Raw API response data
   * @returns Service response with seeding results
   */
  async seedFromApiData(apiData: NestApiResponse): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      this.log(
        "info",
        `Starting seeding process for ${apiData.records.length} records`
      );

      const results = {
        processed: 0,
        created: 0,
        updated: 0,
        errors: 0,
        errorDetails: [] as any[],
      };

      // Process each record
      for (const record of apiData.records) {
        try {
          results.processed++;

          // Transform the record
          const transformedData = this.transformApiRecord(record);

          // Use upsert to handle both create and update operations
          await this.upsertRecord({
            where: { ocid: transformedData.ocid },
            update: {
              ...transformedData,
              updatedAt: new Date(),
            },
            create: transformedData,
          });
        } catch (error) {
          results.errors++;
          const errorDetail = {
            ocid: record.ocid,
            error: error instanceof Error ? error.message : "Unknown error",
          };
          results.errorDetails.push(errorDetail);
          this.log(
            "error",
            `Failed to process tender ${record.ocid}: ${errorDetail.error}`
          );
        }
      }

      this.log(
        "info",
        `Seeding completed: ${results.created} created, ${results.updated} updated, ${results.errors} errors`
      );

      return {
        success: true,
        data: results,
        message: `Processed ${results.processed} records: ${results.created} created, ${results.updated} updated, ${results.errors} errors`,
      };
    }, "seedFromApiData");
  }

  /**
   * Seed a single tender record
   * @param tenderData - Single tender data to seed
   * @param skipCronCheck - Skip the 12-hour interval check (default: false)
   * @returns Service response with seeding result
   */
  async seedSingleRecord(
    tenderData: NestTenderData,
    skipCronCheck: boolean = false
  ): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      this.log("info", `Seeding single tender record: ${tenderData.ocid}`);

      // Check execution interval using nest_tender_cron_log (unless skipped)
      const currentTime = new Date();
      if (!skipCronCheck) {
        const cronLog = await this.getCronLog();

        // Skip if current time is less than next_run (12-hour interval not reached)
        if (
          cronLog &&
          cronLog.next_run &&
          currentTime < new Date(cronLog.next_run)
        ) {
          const timeUntilNext =
            new Date(cronLog.next_run).getTime() - currentTime.getTime();
          const hoursUntilNext =
            Math.round(timeUntilNext / (1000 * 60 * 60 * 100)) / 10;

          this.log(
            "info",
            `Skipping single record seed - next run scheduled in ${hoursUntilNext} hours`
          );
          return {
            success: true,
            skipped: true,
            message: `Single record seeding skipped - next run scheduled in ${hoursUntilNext} hours`,
            nextRun: cronLog.next_run,
          };
        }
      }

      try {
        // Check if record already exists (for action determination)
        const existingRecord = await this.findUniqueRecord({
          where: { ocid: tenderData.ocid },
          select: { id: true, updatedAt: true },
        });

        // Use upsert to handle both create and update operations
        const result = await this.upsertRecord({
          where: { ocid: tenderData.ocid },
          update: {
            ...tenderData,
            updatedAt: new Date(),
          },
          create: tenderData,
        });

        const action: "created" | "updated" = existingRecord
          ? "updated"
          : "created";
        this.log(
          "info",
          `${action === "created" ? "Created" : "Updated"} tender: ${
            tenderData.ocid
          }`
        );

        // Update cron log if not skipped
        let cronLogInfo = null;
        if (!skipCronCheck) {
          cronLogInfo = await this.manageCronLog(currentTime);
        }

        return {
          success: true,
          data: result,
          action,
          message: `Tender ${action} successfully: ${tenderData.ocid}`,
          cronLog: cronLogInfo,
        };
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error";
        this.log(
          "error",
          `Failed to seed tender ${tenderData.ocid}: ${errorMessage}`
        );
        throw new Error(
          `Failed to seed tender ${tenderData.ocid}: ${errorMessage}`
        );
      }
    }, "seedSingleRecord");
  }

  /**
   * Get seeding parameters for external API requests
   * @param customOffset - Custom offset value (if null, will calculate based on existing records)
   * @param daysSince - Number of days back to fetch data from (default: 30)
   * @param useDynamicOffset - Whether to use dynamic offset based on existing records (default: true)
   * @returns URL parameters string with offset and since datetime
   */
  private async getSeedingParams(
    customOffset: number | null = null,
    daysSince: number = 30,
    useDynamicOffset: boolean = true
  ): Promise<string> {
    const currentDate = new Date();
    const sinceDate = new Date(
      currentDate.getTime() - daysSince * 24 * 60 * 60 * 1000
    );

    let offset = customOffset;

    // Calculate dynamic offset if not provided and useDynamicOffset is true
    if (offset === null && useDynamicOffset) {
      try {
        offset = await this.getLastModelRowIndexByDays(daysSince);
        this.log(
          "info",
          `Calculated dynamic offset: ${offset} based on existing records`
        );
      } catch (error) {
        this.log(
          "warn",
          `Failed to calculate dynamic offset, using 0: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
        offset = 0;
      }
    } else if (offset === null) {
      offset = 0;
    }

    const params = new URLSearchParams({
      offset: offset.toString(),
      since: sinceDate.toISOString(),
    });

    this.log(
      "info",
      `Generated seeding params: offset=${offset}, since=${sinceDate.toISOString()}`
    );
    return `?${params.toString()}`;
  }

  /**
   * Get the complete seeding URL with base URL and parameters
   * @param customOffset - Custom offset value (if null, will calculate based on existing records)
   * @param daysSince - Number of days back to fetch data from (default: 30)
   * @param useDynamicOffset - Whether to use dynamic offset based on existing records (default: true)
   * @returns Complete URL for external API seeding
   */
  private async seedingBaseURL(
    customOffset: number | null = null,
    daysSince: number = 30,
    useDynamicOffset: boolean = true
  ): Promise<string> {
    const baseURL: string = process.env.NEST_API_URL!;
    const params = await this.getSeedingParams(
      customOffset,
      daysSince,
      useDynamicOffset
    );
    const fullURL = baseURL + params;

    this.log("info", `Generated seeding URL: ${fullURL}`);
    return fullURL;
  }

  /**
   * Fetch data from external API and seed database in bulk
   * @param options - Fetch options
   * @returns Service response with seeding results
   */
  async bulkSeedFromExternalApi(
    options: RequestInit = {}
  ): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      const fullURL = await this.seedingBaseURL();
      this.log("info", `Fetching data from external API: ${fullURL}`);

      // Check execution interval using nest_tender_cron_log
      const currentTime = new Date();
      const cronLog = await this.getCronLog();

      // Skip if current time is less than next_run (12-hour interval not reached)
      if (
        cronLog &&
        cronLog.next_run &&
        currentTime < new Date(cronLog.next_run)
      ) {
        const timeUntilNext =
          new Date(cronLog.next_run).getTime() - currentTime.getTime();
        const hoursUntilNext =
          Math.round(timeUntilNext / (1000 * 60 * 60 * 100)) / 10;

        this.log(
          "info",
          `Skipping bulk seed - next run scheduled in ${hoursUntilNext} hours`
        );
        return {
          success: true,
          skipped: true,
          message: `Bulk seeding skipped - next run scheduled in ${hoursUntilNext} hours`,
          nextRun: cronLog.next_run,
        };
      }

      try {
        // Fetch data from external API
        const response = await fetch(fullURL, {
          headers: {
            "Content-Type": "application/json",
            ...options.headers,
          },
          ...options,
        });

        if (!response.ok) {
          throw new Error(
            `API request failed: ${response.status} ${response.statusText}`
          );
        }

        const apiData = await response.json();
        this.log(
          "info",
          `Fetched ${apiData.records?.length || 0} records from external API`
        );

        // Seed the database with fetched data
        const seedResult = await this.seedFromApiData(
          apiData as NestApiResponse
        );

        // Upsert the nest_tender_cron_log using helper method
        const cronLogInfo = await this.manageCronLog(currentTime);

        return {
          ...seedResult,
          cronLog: cronLogInfo,
        };
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error occurred";
        this.log("error", `Failed to fetch from external API: ${errorMessage}`);
        throw new Error(`External API seeding failed: ${errorMessage}`);
      }
    }, "bulkSeedFromExternalApi");
  }

  /**
   * Bulk upsert tenders with batch processing
   * @param tenderDataArray - Array of tender data to upsert
   * @param batchSize - Number of records to process in each batch
   * @returns Service response with bulk upsert results
   */
  async bulkUpsertTenders(
    tenderDataArray: NestTenderData[],
    batchSize: number = 50
  ): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      this.log(
        "info",
        `Starting bulk upsert for ${tenderDataArray.length} tenders`
      );

      const results = {
        processed: 0,
        created: 0,
        updated: 0,
        errors: 0,
        errorDetails: [] as any[],
      };

      // Process in batches
      for (let i = 0; i < tenderDataArray.length; i += batchSize) {
        const batch = tenderDataArray.slice(i, i + batchSize);
        this.log(
          "info",
          `Processing batch ${Math.floor(i / batchSize) + 1} (${
            batch.length
          } records)`
        );

        // Process each record in the batch
        for (const tenderData of batch) {
          try {
            results.processed++;
            const result = await this.upsertTender(tenderData);

            if (result.success) {
              // Check if it was an update or create by looking for existing record
              const existing = await this.findUniqueRecord({
                where: { ocid: tenderData.ocid },
                select: { createdAt: true, updatedAt: true },
              });

              if (
                existing &&
                (existing as any).createdAt !== (existing as any).updatedAt
              ) {
                results.updated++;
              } else {
                results.created++;
              }
            }
          } catch (error) {
            results.errors++;
            const errorDetail = {
              ocid: tenderData.ocid,
              error: error instanceof Error ? error.message : "Unknown error",
            };
            results.errorDetails.push(errorDetail);
            this.log(
              "error",
              `Failed to upsert tender ${tenderData.ocid}: ${errorDetail.error}`
            );
          }
        }

        // Small delay between batches to prevent overwhelming the database
        if (i + batchSize < tenderDataArray.length) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      }

      this.log(
        "info",
        `Bulk upsert completed: ${results.created} created, ${results.updated} updated, ${results.errors} errors`
      );

      return {
        success: true,
        data: results,
        message: `Processed ${results.processed} records: ${results.created} created, ${results.updated} updated, ${results.errors} errors`,
      };
    }, "bulkUpsertTenders");
  }

  /**
   * Unified seeding method that sequences bulk and single record operations
   * @param options - Seeding configuration options
   * @returns Service response with comprehensive seeding results
   */
  async seedTenders(
    options: {
      useBulkSeeding?: boolean;
      fallbackToSingleRecords?: boolean;
      singleRecordData?: NestTenderData[];
      skipCronCheck?: boolean;
      batchSize?: number;
      maxRetries?: number;
    } = {}
  ): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      const {
        useBulkSeeding = true,
        fallbackToSingleRecords = true,
        singleRecordData = [],
        skipCronCheck = false,
        batchSize = 50,
        maxRetries = 3,
      } = options;

      this.log("info", "Starting unified seeding process");

      const results = {
        bulkSeeding: null as any,
        singleRecordSeeding: null as any,
        totalProcessed: 0,
        totalCreated: 0,
        totalUpdated: 0,
        totalErrors: 0,
        executionSequence: [] as string[],
        errors: [] as any[],
      };

      // Step 1: Attempt bulk seeding from external API
      if (useBulkSeeding) {
        try {
          this.log("info", "Attempting bulk seeding from external API");
          results.executionSequence.push("bulk_seeding_attempted");

          const bulkResult = await this.bulkSeedFromExternalApi();

          if (bulkResult.success && !(bulkResult as any).skipped) {
            results.bulkSeeding = bulkResult;
            results.totalProcessed += bulkResult.data?.processed || 0;
            results.totalCreated += bulkResult.data?.created || 0;
            results.totalUpdated += bulkResult.data?.updated || 0;
            results.totalErrors += bulkResult.data?.errors || 0;
            results.executionSequence.push("bulk_seeding_completed");

            this.log(
              "info",
              `Bulk seeding completed: ${
                bulkResult.data?.processed || 0
              } records processed`
            );
          } else if ((bulkResult as any).skipped) {
            results.bulkSeeding = bulkResult;
            results.executionSequence.push("bulk_seeding_skipped");

            this.log("info", `Bulk seeding skipped: ${bulkResult.message}`);
          }
        } catch (error) {
          const errorMessage =
            error instanceof Error
              ? error.message
              : "Unknown bulk seeding error";
          results.errors.push({
            type: "bulk_seeding",
            error: errorMessage,
          });
          results.executionSequence.push("bulk_seeding_failed");

          this.log("error", `Bulk seeding failed: ${errorMessage}`);
        }
      }

      // Step 2: Process single records if provided or as fallback
      if (
        singleRecordData.length > 0 ||
        (fallbackToSingleRecords &&
          (!results.bulkSeeding || (results.bulkSeeding as any).skipped))
      ) {
        try {
          this.log(
            "info",
            `Processing single records: ${singleRecordData.length} records`
          );
          results.executionSequence.push("single_record_seeding_attempted");

          const singleRecordResults = {
            processed: 0,
            created: 0,
            updated: 0,
            errors: 0,
            errorDetails: [] as any[],
          };

          // Process single records in batches
          for (let i = 0; i < singleRecordData.length; i += batchSize) {
            const batch = singleRecordData.slice(i, i + batchSize);
            this.log(
              "info",
              `Processing single record batch ${
                Math.floor(i / batchSize) + 1
              } (${batch.length} records)`
            );

            for (const tenderData of batch) {
              let retryCount = 0;
              let success = false;

              while (retryCount < maxRetries && !success) {
                try {
                  const result = await this.seedSingleRecord(
                    tenderData,
                    skipCronCheck
                  );

                  if (result.success && !(result as any).skipped) {
                    singleRecordResults.processed++;
                    if ((result as any).action === "created") {
                      singleRecordResults.created++;
                    } else if ((result as any).action === "updated") {
                      singleRecordResults.updated++;
                    }
                    success = true;

                    this.log(
                      "info",
                      `Single record ${(result as any).action}: ${
                        tenderData.ocid
                      }`
                    );
                  } else if ((result as any).skipped) {
                    this.log(
                      "info",
                      `Single record skipped: ${tenderData.ocid} - ${result.message}`
                    );
                    success = true; // Don't retry skipped records
                  }
                } catch (error) {
                  retryCount++;
                  const errorMessage =
                    error instanceof Error ? error.message : "Unknown error";

                  if (retryCount >= maxRetries) {
                    singleRecordResults.errors++;
                    singleRecordResults.errorDetails.push({
                      ocid: tenderData.ocid,
                      error: errorMessage,
                      retries: retryCount,
                    });

                    this.log(
                      "error",
                      `Single record failed after ${retryCount} retries: ${tenderData.ocid} - ${errorMessage}`
                    );
                  } else {
                    this.log(
                      "warn",
                      `Single record retry ${retryCount}/${maxRetries}: ${tenderData.ocid} - ${errorMessage}`
                    );
                    // Small delay before retry
                    await new Promise((resolve) =>
                      setTimeout(resolve, 1000 * retryCount)
                    );
                  }
                }
              }
            }

            // Small delay between batches
            if (i + batchSize < singleRecordData.length) {
              await new Promise((resolve) => setTimeout(resolve, 100));
            }
          }

          results.singleRecordSeeding = {
            success: true,
            data: singleRecordResults,
            message: `Processed ${singleRecordResults.processed} single records: ${singleRecordResults.created} created, ${singleRecordResults.updated} updated, ${singleRecordResults.errors} errors`,
          };

          results.totalProcessed += singleRecordResults.processed;
          results.totalCreated += singleRecordResults.created;
          results.totalUpdated += singleRecordResults.updated;
          results.totalErrors += singleRecordResults.errors;
          results.executionSequence.push("single_record_seeding_completed");

          this.log(
            "info",
            `Single record seeding completed: ${singleRecordResults.processed} records processed`
          );
        } catch (error) {
          const errorMessage =
            error instanceof Error
              ? error.message
              : "Unknown single record seeding error";
          results.errors.push({
            type: "single_record_seeding",
            error: errorMessage,
          });
          results.executionSequence.push("single_record_seeding_failed");

          this.log("error", `Single record seeding failed: ${errorMessage}`);
        }
      }

      // Step 3: Generate comprehensive summary
      const summary = this.generateSeedingSummary(results);

      this.log(
        "info",
        `Unified seeding completed: ${results.totalProcessed} total processed, ${results.totalCreated} created, ${results.totalUpdated} updated, ${results.totalErrors} errors`
      );

      return {
        success: true,
        data: results,
        message: summary,
        executionSequence: results.executionSequence,
      };
    }, "seedTenders");
  }

  /**
   * Generate a comprehensive summary of seeding results
   * @param results - Seeding results data
   * @returns Formatted summary string
   */
  private generateSeedingSummary(results: any): string {
    const summaryParts = [];

    // Overall summary
    summaryParts.push(
      `Total: ${results.totalProcessed} processed, ${results.totalCreated} created, ${results.totalUpdated} updated, ${results.totalErrors} errors`
    );

    // Bulk seeding summary
    if (results.bulkSeeding) {
      if ((results.bulkSeeding as any).skipped) {
        summaryParts.push(
          `Bulk seeding: Skipped (${results.bulkSeeding.message})`
        );
      } else if (results.bulkSeeding.success) {
        const bulkData = results.bulkSeeding.data;
        summaryParts.push(
          `Bulk seeding: ${bulkData?.processed || 0} processed, ${
            bulkData?.created || 0
          } created, ${bulkData?.updated || 0} updated, ${
            bulkData?.errors || 0
          } errors`
        );
      }
    }

    // Single record summary
    if (results.singleRecordSeeding) {
      const singleData = results.singleRecordSeeding.data;
      summaryParts.push(
        `Single records: ${singleData.processed} processed, ${singleData.created} created, ${singleData.updated} updated, ${singleData.errors} errors`
      );
    }

    // Error summary
    if (results.errors.length > 0) {
      summaryParts.push(`System errors: ${results.errors.length} encountered`);
    }

    return summaryParts.join(" | ");
  }

  /**
   * Helper method to manage cron log operations using base service methods
   * @param currentTime - Current execution time
   * @returns Promise with cron log information
   */
  private async manageCronLog(
    currentTime: Date
  ): Promise<{ lastRun: Date; nextRun: Date }> {
    // Temporarily switch to nest_tender_cron_log model
    const originalModel = this.getModel();
    this.setModel((prisma as any).nest_tender_cron_log);

    try {
      const nextRunTime = new Date(currentTime.getTime() + 12 * 60 * 60 * 1000); // 12 hours from now

      // Use base service upsert method
      await this.upsertRecord({
        where: { id: "default" },
        update: {
          last_run: currentTime,
          next_run: nextRunTime,
        },
        create: {
          id: "default",
          last_run: currentTime,
          next_run: nextRunTime,
        },
      });

      this.log(
        "info",
        `Updated cron log - next run scheduled for: ${nextRunTime.toISOString()}`
      );

      return {
        lastRun: currentTime,
        nextRun: nextRunTime,
      };
    } finally {
      // Restore original model
      if (originalModel) {
        this.setModel(originalModel);
      }
    }
  }

  /**
   * Helper method to check cron log interval using base service methods
   * @returns Promise with cron log data or null if not found
   */
  private async getCronLog(): Promise<any> {
    // Temporarily switch to nest_tender_cron_log model
    const originalModel = this.getModel();
    this.setModel((prisma as any).nest_tender_cron_log);

    try {
      return await this.findFirstRecord({
        orderBy: { last_run: "desc" },
      });
    } finally {
      // Restore original model
      if (originalModel) {
        this.setModel(originalModel);
      }
    }
  }
}
