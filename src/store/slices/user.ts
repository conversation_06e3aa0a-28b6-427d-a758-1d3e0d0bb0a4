import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  searchUsers,
  getUser,
  createClient,
  createEmployee,
  updateClient,
  updateEmployee,
  updateUser,
  deleteUser,
  performBulkUserAction,
  getUserStatistics,
} from "@/store/actions/user";
import type {
  User,
  UserStatistics,
  UserType,
} from "@/lib/api/validators/schemas/user";

// User state interface
export interface UserState {
  // Data
  users: User[];
  currentUser: User | null;
  statistics: UserStatistics | null;

  // Loading states
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  isBulkActionLoading: boolean;
  isStatisticsLoading: boolean;

  // Error states
  error: string | null;

  // Search and filtering
  searchQuery: string;
  filters: {
    type?: UserType;
    status?: string;
    companyId?: string;
    roleId?: string;
  };

  // Pagination
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };

  // UI state
  selectedUserIds: string[];
}

// Initial state
const initialState: UserState = {
  // Data
  users: [],
  currentUser: null,
  statistics: null,

  // Loading states
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  isBulkActionLoading: false,
  isStatisticsLoading: false,

  // Error states
  error: null,

  // Search and filtering
  searchQuery: "",
  filters: {},

  // Pagination
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  },

  // UI state
  selectedUserIds: [],
};

// User slice
const userSlice = createSlice({
  name: "users",
  initialState,
  reducers: {
    // Loading states
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setCreating: (state, action: PayloadAction<boolean>) => {
      state.isCreating = action.payload;
    },
    setUpdating: (state, action: PayloadAction<boolean>) => {
      state.isUpdating = action.payload;
    },
    setDeleting: (state, action: PayloadAction<boolean>) => {
      state.isDeleting = action.payload;
    },
    setBulkActionLoading: (state, action: PayloadAction<boolean>) => {
      state.isBulkActionLoading = action.payload;
    },
    setStatisticsLoading: (state, action: PayloadAction<boolean>) => {
      state.isStatisticsLoading = action.payload;
    },

    // Error handling
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },

    // Data management
    setCurrentUser: (state, action: PayloadAction<User | null>) => {
      state.currentUser = action.payload;
    },

    // Search and filtering
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    setFilters: (
      state,
      action: PayloadAction<Partial<UserState["filters"]>>
    ) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {};
      state.searchQuery = "";
    },

    // Pagination
    setPagination: (
      state,
      action: PayloadAction<Partial<UserState["pagination"]>>
    ) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },

    // Selection management
    setSelectedUserIds: (state, action: PayloadAction<string[]>) => {
      state.selectedUserIds = action.payload;
    },
    toggleUserSelection: (state, action: PayloadAction<string>) => {
      const userId = action.payload;
      const index = state.selectedUserIds.indexOf(userId);
      if (index > -1) {
        state.selectedUserIds.splice(index, 1);
      } else {
        state.selectedUserIds.push(userId);
      }
    },
    clearSelection: (state) => {
      state.selectedUserIds = [];
    },

    // Reset state
    resetState: (state) => {
      Object.assign(state, initialState);
    },
  },
  extraReducers: (builder) => {
    // Search users
    builder.addCase(searchUsers.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(searchUsers.fulfilled, (state, action) => {
      state.isLoading = false;
      state.users = action.payload.data || [];
      if (action.payload.pagination) {
        state.pagination = {
          ...state.pagination,
          ...action.payload.pagination,
        };
      }
    });
    builder.addCase(searchUsers.rejected, (state, action) => {
      state.isLoading = false;
      state.error =
        (action.payload as any)?.message || "Failed to search users";
    });

    // Get user
    builder.addCase(getUser.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(getUser.fulfilled, (state, action) => {
      state.isLoading = false;
      state.currentUser = action.payload;
    });
    builder.addCase(getUser.rejected, (state, action) => {
      state.isLoading = false;
      state.error = (action.payload as any)?.message || "Failed to get user";
    });

    // Create client
    builder.addCase(createClient.pending, (state) => {
      state.isCreating = true;
      state.error = null;
    });
    builder.addCase(createClient.fulfilled, (state, action) => {
      state.isCreating = false;
      state.users.unshift(action.payload);
    });
    builder.addCase(createClient.rejected, (state, action) => {
      state.isCreating = false;
      state.error =
        (action.payload as any)?.message || "Failed to create client";
    });

    // Create employee
    builder.addCase(createEmployee.pending, (state) => {
      state.isCreating = true;
      state.error = null;
    });
    builder.addCase(createEmployee.fulfilled, (state, action) => {
      state.isCreating = false;
      state.users.unshift(action.payload);
    });
    builder.addCase(createEmployee.rejected, (state, action) => {
      state.isCreating = false;
      state.error =
        (action.payload as any)?.message || "Failed to create employee";
    });

    // Update client
    builder.addCase(updateClient.pending, (state) => {
      state.isUpdating = true;
      state.error = null;
    });
    builder.addCase(updateClient.fulfilled, (state, action) => {
      state.isUpdating = false;
      const index = state.users.findIndex(
        (user) => user.id === action.payload.id
      );
      if (index !== -1) {
        state.users[index] = action.payload;
      }
      if (state.currentUser?.id === action.payload.id) {
        state.currentUser = action.payload;
      }
    });
    builder.addCase(updateClient.rejected, (state, action) => {
      state.isUpdating = false;
      state.error =
        (action.payload as any)?.message || "Failed to update client";
    });

    // Update employee
    builder.addCase(updateEmployee.pending, (state) => {
      state.isUpdating = true;
      state.error = null;
    });
    builder.addCase(updateEmployee.fulfilled, (state, action) => {
      state.isUpdating = false;
      const index = state.users.findIndex(
        (user) => user.id === action.payload.id
      );
      if (index !== -1) {
        state.users[index] = action.payload;
      }
      if (state.currentUser?.id === action.payload.id) {
        state.currentUser = action.payload;
      }
    });
    builder.addCase(updateEmployee.rejected, (state, action) => {
      state.isUpdating = false;
      state.error =
        (action.payload as any)?.message || "Failed to update employee";
    });

    // Update user (generic)
    builder.addCase(updateUser.pending, (state) => {
      state.isUpdating = true;
      state.error = null;
    });
    builder.addCase(updateUser.fulfilled, (state, action) => {
      state.isUpdating = false;
      const index = state.users.findIndex(
        (user) => user.id === action.payload.id
      );
      if (index !== -1) {
        state.users[index] = action.payload;
      }
      if (state.currentUser?.id === action.payload.id) {
        state.currentUser = action.payload;
      }
    });
    builder.addCase(updateUser.rejected, (state, action) => {
      state.isUpdating = false;
      state.error = (action.payload as any)?.message || "Failed to update user";
    });

    // Delete user
    builder.addCase(deleteUser.pending, (state) => {
      state.isDeleting = true;
      state.error = null;
    });
    builder.addCase(deleteUser.fulfilled, (state, action) => {
      state.isDeleting = false;
      state.users = state.users.filter((user) => user.id !== action.payload.id);
      if (state.currentUser?.id === action.payload.id) {
        state.currentUser = null;
      }
      // Remove from selection if selected
      state.selectedUserIds = state.selectedUserIds.filter(
        (id) => id !== action.payload.id
      );
    });
    builder.addCase(deleteUser.rejected, (state, action) => {
      state.isDeleting = false;
      state.error = (action.payload as any)?.message || "Failed to delete user";
    });

    // Bulk actions
    builder.addCase(performBulkUserAction.pending, (state) => {
      state.isBulkActionLoading = true;
      state.error = null;
    });
    builder.addCase(performBulkUserAction.fulfilled, (state, action) => {
      state.isBulkActionLoading = false;
      // Clear selection after successful bulk action
      state.selectedUserIds = [];
      // Note: Actual data updates should be handled by refetching or specific logic
    });
    builder.addCase(performBulkUserAction.rejected, (state, action) => {
      state.isBulkActionLoading = false;
      state.error =
        (action.payload as any)?.message || "Failed to perform bulk action";
    });

    // Get statistics
    builder.addCase(getUserStatistics.pending, (state) => {
      state.isStatisticsLoading = true;
      state.error = null;
    });
    builder.addCase(getUserStatistics.fulfilled, (state, action) => {
      state.isStatisticsLoading = false;
      state.statistics = action.payload;
    });
    builder.addCase(getUserStatistics.rejected, (state, action) => {
      state.isStatisticsLoading = false;
      state.error =
        (action.payload as any)?.message || "Failed to get user statistics";
    });
  },
});

// Export actions
export const {
  setLoading,
  setCreating,
  setUpdating,
  setDeleting,
  setBulkActionLoading,
  setStatisticsLoading,
  setError,
  clearError,
  setCurrentUser,
  setSearchQuery,
  setFilters,
  clearFilters,
  setPagination,
  setSelectedUserIds,
  toggleUserSelection,
  clearSelection,
  resetState,
} = userSlice.actions;

// Export reducer
export default userSlice;

// Selectors
export const selectUsers = (state: { users: UserState }) => state.users.users;
export const selectCurrentUser = (state: { users: UserState }) =>
  state.users.currentUser;
export const selectUserStatistics = (state: { users: UserState }) =>
  state.users.statistics;

// Loading selectors
export const selectIsLoading = (state: { users: UserState }) =>
  state.users.isLoading;
export const selectIsCreating = (state: { users: UserState }) =>
  state.users.isCreating;
export const selectIsUpdating = (state: { users: UserState }) =>
  state.users.isUpdating;
export const selectIsDeleting = (state: { users: UserState }) =>
  state.users.isDeleting;
export const selectIsBulkActionLoading = (state: { users: UserState }) =>
  state.users.isBulkActionLoading;
export const selectIsStatisticsLoading = (state: { users: UserState }) =>
  state.users.isStatisticsLoading;

// Error selectors
export const selectError = (state: { users: UserState }) => state.users.error;

// Search and filter selectors
export const selectSearchQuery = (state: { users: UserState }) =>
  state.users.searchQuery;
export const selectFilters = (state: { users: UserState }) =>
  state.users.filters;

// Pagination selectors
export const selectPagination = (state: { users: UserState }) =>
  state.users.pagination;

// Selection selectors
export const selectSelectedUserIds = (state: { users: UserState }) =>
  state.users.selectedUserIds;
export const selectSelectedUsers = (state: { users: UserState }) => {
  const users = state.users.users;
  const selectedIds = state.users.selectedUserIds;
  return users.filter((user) => selectedIds.includes(user.id));
};

// Computed selectors
export const selectUsersByType =
  (type: UserType) => (state: { users: UserState }) => {
    return state.users.users.filter((user) => user.type === type);
  };

export const selectActiveUsers = (state: { users: UserState }) => {
  return state.users.users.filter((user) => user.status === "active");
};

export const selectInactiveUsers = (state: { users: UserState }) => {
  return state.users.users.filter((user) => user.status === "inactive");
};

export const selectUserById = (id: string) => (state: { users: UserState }) => {
  return state.users.users.find((user) => user.id === id);
};
