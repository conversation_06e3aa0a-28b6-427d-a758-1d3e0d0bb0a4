"use client";

import { createAsyncThunk } from "@reduxjs/toolkit";
import { toast } from "sonner";
import { api } from "@/lib/common/requests";
import type {
  CreateClient,
  CreateEmployee,
  UpdateClient,
  UpdateEmployee,
  UpdateUser,
  UserQ<PERSON>y,
  Bulk<PERSON>ser,
} from "@/lib/api/validators/schemas/user";

// Note: API endpoints are now handled directly in the action functions
// - Employee operations use "/api/employee"
// - Client operations use "/api/client"
// - Generic user operations use "/api/user"

// Types for user actions
export interface UserSearchParams {
  id?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  type?: "admin" | "employee" | "client";
  status?: string;
  companyId?: string;
  roleId?: string;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?:
    | "firstName"
    | "lastName"
    | "email"
    | "type"
    | "status"
    | "createdAt"
    | "updatedAt";
  sortOrder?: "asc" | "desc";
  includeRole?: boolean;
  includeCompany?: boolean;
}

// Async thunk actions

/**
 * Search users with filtering and pagination
 */
export const searchUsers = createAsyncThunk(
  "users/searchUsers",
  async (params: UserSearchParams = {}, { rejectWithValue }) => {
    try {
      const queryParams = new URLSearchParams();

      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          queryParams.append(key, value.toString());
        }
      });

      // Determine API endpoint based on user type
      let apiEndpoint = `user?${queryParams.toString()}`;
      if (params.type === "employee") {
        apiEndpoint = `employee?${queryParams.toString()}`;
      } else if (params.type === "client") {
        apiEndpoint = `client?${queryParams.toString()}`;
      }

      const response = await api.get<any>(apiEndpoint);

      if (response.success && !response.error) {
        return response.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to search users",
        });
      }
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to search users";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Search employees specifically
 */
export const searchEmployees = createAsyncThunk(
  "users/searchEmployees",
  async (params: Omit<UserSearchParams, "type"> = {}, { rejectWithValue }) => {
    try {
      const queryParams = new URLSearchParams();

      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          queryParams.append(key, value.toString());
        }
      });

      const response = await api.get<any>(`employee?${queryParams.toString()}`);

      if (response.success && !response.error) {
        return response.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to search employees",
        });
      }
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to search employees";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Search clients specifically
 */
export const searchClients = createAsyncThunk(
  "users/searchClients",
  async (params: Omit<UserSearchParams, "type"> = {}, { rejectWithValue }) => {
    try {
      const queryParams = new URLSearchParams();

      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          queryParams.append(key, value.toString());
        }
      });

      const response = await api.get<any>(`client?${queryParams.toString()}`);

      if (response.success && !response.error) {
        return response.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to search clients",
        });
      }
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to search clients";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Get a single user by ID
 */
export const getUser = createAsyncThunk(
  "users/getUser",
  async (userId: string, { rejectWithValue }) => {
    try {
      const response = await api.get<any>(`user?id=${userId}`);

      if (response.success && !response.error) {
        return response.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to get user",
        });
      }
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to get user";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Create a new client user
 */
export const createClient = createAsyncThunk(
  "users/createClient",
  async (clientData: CreateClient, { rejectWithValue }) => {
    try {
      const response = await api.post<any>("client", clientData);

      if (response.success && !response.error) {
        toast.success("Client created successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to create client");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to create client",
        });
      }
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to create client";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Create a new employee user
 */
export const createEmployee = createAsyncThunk(
  "users/createEmployee",
  async (employeeData: CreateEmployee, { rejectWithValue }) => {
    try {
      const response = await api.post<any>("employee", employeeData);

      if (response.success && !response.error) {
        toast.success("Employee created successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to create employee");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to create employee",
        });
      }
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to create employee";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Update a client user
 */
export const updateClient = createAsyncThunk(
  "users/updateClient",
  async (
    { id, data }: { id: string; data: Partial<UpdateClient> },
    { rejectWithValue }
  ) => {
    try {
      const response = await api.put<any>("client", {
        id,
        ...data,
      });

      if (response.success && !response.error) {
        toast.success("Client updated successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to update client");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to update client",
        });
      }
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to update client";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Update an employee user
 */
export const updateEmployee = createAsyncThunk(
  "users/updateEmployee",
  async (
    { id, data }: { id: string; data: Partial<UpdateEmployee> },
    { rejectWithValue }
  ) => {
    try {
      const response = await api.put<any>("employee", { id, ...data });

      if (response.success && !response.error) {
        toast.success("Employee updated successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to update employee");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to update employee",
        });
      }
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to update employee";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Generic update user
 */
export const updateUser = createAsyncThunk(
  "users/updateUser",
  async (
    { id, data }: { id: string; data: Partial<UpdateUser> },
    { rejectWithValue }
  ) => {
    try {
      const response = await api.put<any>("user", { id, ...data });

      if (response.success && !response.error) {
        toast.success("User updated successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to update user");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to update user",
        });
      }
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to update user";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Delete a user (generic)
 */
export const deleteUser = createAsyncThunk(
  "users/deleteUser",
  async (userId: string, { rejectWithValue }) => {
    try {
      const response = await api.delete<any>(`user?id=${userId}`);

      if (response.success && !response.error) {
        toast.success("User deleted successfully");
        return { id: userId, ...response.data };
      } else {
        toast.error(response.message || "Failed to delete user");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to delete user",
        });
      }
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to delete user";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Delete an employee
 */
export const deleteEmployee = createAsyncThunk(
  "users/deleteEmployee",
  async (employeeId: string, { rejectWithValue }) => {
    try {
      const response = await api.delete<any>(`employee?id=${employeeId}`);

      if (response.success && !response.error) {
        toast.success("Employee deleted successfully");
        return { id: employeeId, ...response.data };
      } else {
        toast.error(response.message || "Failed to delete employee");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to delete employee",
        });
      }
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to delete employee";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Delete a client
 */
export const deleteClient = createAsyncThunk(
  "users/deleteClient",
  async (clientId: string, { rejectWithValue }) => {
    try {
      const response = await api.delete<any>(`client?id=${clientId}`);

      if (response.success && !response.error) {
        toast.success("Client deleted successfully");
        return { id: clientId, ...response.data };
      } else {
        toast.error(response.message || "Failed to delete client");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to delete client",
        });
      }
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to delete client";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Perform bulk action on users
 */
export const performBulkUserAction = createAsyncThunk(
  "users/performBulkAction",
  async (bulkData: BulkUser, { rejectWithValue }) => {
    try {
      const response = await api.post<any>("user/bulk", bulkData);

      if (response.success && !response.error) {
        toast.success(`Bulk ${bulkData.action} completed successfully`);
        return response.data;
      } else {
        toast.error(
          response.message || `Failed to perform bulk ${bulkData.action}`
        );
        return rejectWithValue({
          success: false,
          error: true,
          message:
            response.message || `Failed to perform bulk ${bulkData.action}`,
        });
      }
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        `Failed to perform bulk ${bulkData.action}`;
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Get user statistics
 */
export const getUserStatistics = createAsyncThunk(
  "users/getUserStatistics",
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get<any>("user/statistics");

      if (response.success && !response.error) {
        return response.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to get user statistics",
        });
      }
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to get user statistics";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Export types for use in other files
export type {
  CreateClient,
  CreateEmployee,
  UpdateClient,
  UpdateEmployee,
  UpdateUser,
  UserQuery,
  BulkUser,
};
