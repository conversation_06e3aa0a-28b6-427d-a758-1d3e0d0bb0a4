import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { UserService } from "@/lib/api/services/user";

/**
 * Client API Route Handler
 *
 * Handles CRUD operations specifically for client users:
 * - GET: Search/list clients with filtering
 * - POST: Create new client
 * - PUT: Update existing client
 * - DELETE: Delete client
 */

// GET /api/client - Search/list clients with filtering
export async function GET(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    // Only admins can list clients
    if (session.user.type !== "admin") {
      return NextResponse.json(
        { success: false, error: true, message: "Admin access required" },
        { status: 403 }
      );
    }

    // Extract query parameters from URL
    const { searchParams } = new URL(request.url);

    // Build query parameters with proper types, forcing client type
    const queryParams: any = {
      type: "client", // Force client type
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "10"),
      sortBy: searchParams.get("sortBy") || "createdAt",
      sortOrder: searchParams.get("sortOrder") || "desc",
      includeRole: searchParams.get("includeRole") === "true",
      includeCompany: searchParams.get("includeCompany") === "true" || true, // Default to true for clients
    };

    // Add optional filters
    if (searchParams.get("id")) queryParams.id = searchParams.get("id");
    if (searchParams.get("firstName"))
      queryParams.firstName = searchParams.get("firstName");
    if (searchParams.get("lastName"))
      queryParams.lastName = searchParams.get("lastName");
    if (searchParams.get("email"))
      queryParams.email = searchParams.get("email");
    if (searchParams.get("status"))
      queryParams.status = searchParams.get("status");
    if (searchParams.get("companyId"))
      queryParams.companyId = searchParams.get("companyId");
    if (searchParams.get("search"))
      queryParams.search = searchParams.get("search");

    // Create service instance
    const userService = new UserService();
    userService.setContext({
      session,
      user: session.user,
      request,
    });

    // Use service to search clients
    const result = await userService.searchUsers(queryParams);

    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: result.statusCode || 500 });
    }
  } catch (error: any) {
    console.error("Client search API error:", error);

    return NextResponse.json(
      {
        success: false,
        error: true,
        message: error?.message || "Internal server error",
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// POST /api/client - Create new client
export async function POST(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    // Only admins can create clients
    if (session.user.type !== "admin") {
      return NextResponse.json(
        { success: false, error: true, message: "Admin access required" },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const clientData = { ...body, type: "client" }; // Force client type

    // Create service instance
    const userService = new UserService();
    userService.setContext({
      session,
      user: session.user,
      request,
    });

    // Create client
    const result = await userService.createClient(clientData);

    if (result.success) {
      return NextResponse.json(result, { status: 201 });
    } else {
      return NextResponse.json(result, { status: result.statusCode || 500 });
    }
  } catch (error: any) {
    console.error("Client creation API error:", error);

    return NextResponse.json(
      {
        success: false,
        error: true,
        message: error?.message || "Internal server error",
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// PUT /api/client - Update existing client
export async function PUT(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { id, ...clientData } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: true, message: "Client ID is required" },
        { status: 400 }
      );
    }

    // Users can update their own profile, admins can update any client
    if (session.user.type !== "admin" && session.user.id !== id) {
      return NextResponse.json(
        { success: false, error: true, message: "Access denied" },
        { status: 403 }
      );
    }

    // Create service instance
    const userService = new UserService();
    userService.setContext({
      session,
      user: session.user,
      request,
    });

    // Update client
    const result = await userService.updateClient(id, clientData);

    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: result.statusCode || 500 });
    }
  } catch (error: any) {
    console.error("Client update API error:", error);

    return NextResponse.json(
      {
        success: false,
        error: true,
        message: error?.message || "Internal server error",
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// DELETE /api/client - Delete client
export async function DELETE(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    // Only admins can delete clients
    if (session.user.type !== "admin") {
      return NextResponse.json(
        { success: false, error: true, message: "Admin access required" },
        { status: 403 }
      );
    }

    // Extract client ID from query parameters
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { success: false, error: true, message: "Client ID is required" },
        { status: 400 }
      );
    }

    // Prevent self-deletion
    if (session.user.id === id) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Cannot delete your own account",
        },
        { status: 400 }
      );
    }

    // Create service instance
    const userService = new UserService();
    userService.setContext({
      session,
      user: session.user,
      request,
    });

    // Delete client
    const result = await userService.deleteUser(id);

    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: result.statusCode || 500 });
    }
  } catch (error: any) {
    console.error("Client deletion API error:", error);

    return NextResponse.json(
      {
        success: false,
        error: true,
        message: error?.message || "Internal server error",
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
