import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { UserService } from "@/lib/api/services/user";

/**
 * Employee API Route Handler
 *
 * Handles CRUD operations specifically for employee users:
 * - GET: Search/list employees with filtering
 * - POST: Create new employee
 * - PUT: Update existing employee
 * - DELETE: Delete employee
 */

// GET /api/employee - Search/list employees with filtering
export async function GET(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    // Only admins can list employees
    if (session.user.type !== "admin") {
      return NextResponse.json(
        { success: false, error: true, message: "Admin access required" },
        { status: 403 }
      );
    }

    // Extract query parameters from URL
    const { searchParams } = new URL(request.url);

    // Build query parameters with proper types, forcing employee type
    const queryParams: any = {
      type: "employee", // Force employee type
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "10"),
      sortBy: searchParams.get("sortBy") || "createdAt",
      sortOrder: searchParams.get("sortOrder") || "desc",
      includeRole: searchParams.get("includeRole") === "true" || true, // Default to true for employees
      includeCompany: searchParams.get("includeCompany") === "true",
    };

    // Add optional filters
    if (searchParams.get("id")) queryParams.id = searchParams.get("id");
    if (searchParams.get("firstName"))
      queryParams.firstName = searchParams.get("firstName");
    if (searchParams.get("lastName"))
      queryParams.lastName = searchParams.get("lastName");
    if (searchParams.get("email"))
      queryParams.email = searchParams.get("email");
    if (searchParams.get("status"))
      queryParams.status = searchParams.get("status");
    if (searchParams.get("roleId"))
      queryParams.roleId = searchParams.get("roleId");
    if (searchParams.get("search"))
      queryParams.search = searchParams.get("search");

    // Create service instance
    const userService = new UserService();
    userService.setContext({
      session,
      user: session.user,
      request,
    });

    // Use service to search employees
    const result = await userService.searchUsers(queryParams);

    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: result.statusCode || 500 });
    }
  } catch (error: any) {
    console.error("Employee search API error:", error);

    return NextResponse.json(
      {
        success: false,
        error: true,
        message: error?.message || "Internal server error",
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// POST /api/employee - Create new employee
export async function POST(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    // Only admins can create employees
    if (session.user.type !== "admin") {
      return NextResponse.json(
        { success: false, error: true, message: "Admin access required" },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const employeeData = { ...body, type: "employee" }; // Force employee type

    // Create service instance
    const userService = new UserService();
    userService.setContext({
      session,
      user: session.user,
      request,
    });

    // Create employee
    const result = await userService.createEmployee(employeeData);

    if (result.success) {
      return NextResponse.json(result, { status: 201 });
    } else {
      return NextResponse.json(result, { status: result.statusCode || 500 });
    }
  } catch (error: any) {
    console.error("Employee creation API error:", error);

    return NextResponse.json(
      {
        success: false,
        error: true,
        message: error?.message || "Internal server error",
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// PUT /api/employee - Update existing employee
export async function PUT(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { id, ...employeeData } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: true, message: "Employee ID is required" },
        { status: 400 }
      );
    }

    // Users can update their own profile, admins can update any employee
    if (session.user.type !== "admin" && session.user.id !== id) {
      return NextResponse.json(
        { success: false, error: true, message: "Access denied" },
        { status: 403 }
      );
    }

    // Create service instance
    const userService = new UserService();
    userService.setContext({
      session,
      user: session.user,
      request,
    });

    // Update employee
    const result = await userService.updateEmployee(id, employeeData);

    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: result.statusCode || 500 });
    }
  } catch (error: any) {
    console.error("Employee update API error:", error);

    return NextResponse.json(
      {
        success: false,
        error: true,
        message: error?.message || "Internal server error",
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// DELETE /api/employee - Delete employee
export async function DELETE(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    // Only admins can delete employees
    if (session.user.type !== "admin") {
      return NextResponse.json(
        { success: false, error: true, message: "Admin access required" },
        { status: 403 }
      );
    }

    // Extract employee ID from query parameters
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { success: false, error: true, message: "Employee ID is required" },
        { status: 400 }
      );
    }

    // Prevent self-deletion
    if (session.user.id === id) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Cannot delete your own account",
        },
        { status: 400 }
      );
    }

    // Create service instance
    const userService = new UserService();
    userService.setContext({
      session,
      user: session.user,
      request,
    });

    // Delete employee
    const result = await userService.deleteUser(id);

    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: result.statusCode || 500 });
    }
  } catch (error: any) {
    console.error("Employee deletion API error:", error);

    return NextResponse.json(
      {
        success: false,
        error: true,
        message: error?.message || "Internal server error",
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
