import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { UserService } from "@/lib/api/services/user";

/**
 * User API Route Handler
 *
 * Handles CRUD operations for users with differentiated contexts:
 * - GET: Search/list users with filtering by type (admin, employee, client)
 * - POST: Create new user (client or employee based on type)
 * - PUT: Update existing user
 * - DELETE: Delete user
 */

// GET /api/user - Search/list users with filtering
export async function GET(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    // Only admins can list users
    if (session.user.type !== "admin") {
      return NextResponse.json(
        { success: false, error: true, message: "Admin access required" },
        { status: 403 }
      );
    }

    // Extract query parameters from URL
    const { searchParams } = new URL(request.url);

    // Build query parameters with proper types
    const queryParams: any = {
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "10"),
      sortBy: searchParams.get("sortBy") || "createdAt",
      sortOrder: searchParams.get("sortOrder") || "desc",
      includeRole: searchParams.get("includeRole") === "true",
      includeCompany: searchParams.get("includeCompany") === "true",
    };

    // Add optional filters
    if (searchParams.get("id")) queryParams.id = searchParams.get("id");
    if (searchParams.get("firstName"))
      queryParams.firstName = searchParams.get("firstName");
    if (searchParams.get("lastName"))
      queryParams.lastName = searchParams.get("lastName");
    if (searchParams.get("email"))
      queryParams.email = searchParams.get("email");
    if (searchParams.get("type")) queryParams.type = searchParams.get("type");
    if (searchParams.get("status"))
      queryParams.status = searchParams.get("status");
    if (searchParams.get("companyId"))
      queryParams.companyId = searchParams.get("companyId");
    if (searchParams.get("roleId"))
      queryParams.roleId = searchParams.get("roleId");
    if (searchParams.get("search"))
      queryParams.search = searchParams.get("search");

    // Create service instance
    const userService = new UserService();
    userService.setContext({
      session,
      user: session.user,
      request,
    });

    // Use service to search users
    const result = await userService.searchUsers(queryParams);

    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: result.statusCode || 500 });
    }
  } catch (error: any) {
    console.error("User search API error:", error);

    return NextResponse.json(
      {
        success: false,
        error: true,
        message: error?.message || "Internal server error",
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// POST /api/user - Create new user (client or employee)
export async function POST(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    // Only admins can create users
    if (session.user.type !== "admin") {
      return NextResponse.json(
        { success: false, error: true, message: "Admin access required" },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { type, ...userData } = body;

    if (!type) {
      return NextResponse.json(
        { success: false, error: true, message: "User type is required" },
        { status: 400 }
      );
    }

    // Create service instance
    const userService = new UserService();
    userService.setContext({
      session,
      user: session.user,
      request,
    });

    let result;

    // Route to appropriate creation method based on type
    if (type === "client") {
      result = await userService.createClient({ type, ...userData });
    } else if (type === "employee") {
      result = await userService.createEmployee({ type, ...userData });
    } else {
      return NextResponse.json(
        { success: false, error: true, message: "Invalid user type" },
        { status: 400 }
      );
    }

    if (result.success) {
      return NextResponse.json(result, { status: 201 });
    } else {
      return NextResponse.json(result, { status: result.statusCode || 500 });
    }
  } catch (error: any) {
    console.error("User creation API error:", error);

    return NextResponse.json(
      {
        success: false,
        error: true,
        message: error?.message || "Internal server error",
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// PUT /api/user - Update existing user
export async function PUT(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { id, type, ...userData } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: true, message: "User ID is required" },
        { status: 400 }
      );
    }

    // Users can update their own profile, admins can update any user
    if (session.user.type !== "admin" && session.user.id !== id) {
      return NextResponse.json(
        { success: false, error: true, message: "Access denied" },
        { status: 403 }
      );
    }

    // Create service instance
    const userService = new UserService();
    userService.setContext({
      session,
      user: session.user,
      request,
    });

    let result;

    // Route to appropriate update method based on user type
    if (type === "client") {
      result = await userService.updateClient(id, userData);
    } else if (type === "employee") {
      result = await userService.updateEmployee(id, userData);
    } else {
      // Fallback to generic update if type not specified
      result = await userService.updateUser(id, userData);
    }

    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: result.statusCode || 500 });
    }
  } catch (error: any) {
    console.error("User update API error:", error);

    return NextResponse.json(
      {
        success: false,
        error: true,
        message: error?.message || "Internal server error",
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// DELETE /api/user - Delete user
export async function DELETE(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    // Only admins can delete users
    if (session.user.type !== "admin") {
      return NextResponse.json(
        { success: false, error: true, message: "Admin access required" },
        { status: 403 }
      );
    }

    // Extract user ID from query parameters
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { success: false, error: true, message: "User ID is required" },
        { status: 400 }
      );
    }

    // Prevent self-deletion
    if (session.user.id === id) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Cannot delete your own account",
        },
        { status: 400 }
      );
    }

    // Create service instance
    const userService = new UserService();
    userService.setContext({
      session,
      user: session.user,
      request,
    });

    // Delete user
    const result = await userService.deleteUser(id);

    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: result.statusCode || 500 });
    }
  } catch (error: any) {
    console.error("User deletion API error:", error);

    return NextResponse.json(
      {
        success: false,
        error: true,
        message: error?.message || "Internal server error",
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
